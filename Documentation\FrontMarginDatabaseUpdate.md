# Front Margin Database Schema Update

## Overview
Cập nhật database schema để support 4 loại Front Margin discount types:

1. **Case I.1**: <PERSON><PERSON><PERSON> khấu theo phần trăm
2. **Case I.2**: <PERSON><PERSON><PERSON> khấu số tiền cố định  
3. **Case II**: <PERSON><PERSON> hàng tặng hàng cùng loại
4. **Case III**: Tặng hàng khác loại

## Database Changes

### 1. PromotionFrontMargin Entity Updates

#### New Fields Added:
```csharp
// Discount Type Identifier
public int DiscountType { get; set; } = 1;

// Case I.2: Fixed Amount Discount
public decimal? FixedDiscountAmount { get; set; }

// Case II: Same Item Gift
public decimal? BuyQuantity { get; set; }
public decimal? GiftQuantity { get; set; }

// Case III: Different Item Gift
public string? GiftItemNumber { get; set; }
public string? GiftItemName { get; set; }
public string? GiftItemUOM { get; set; }
public decimal? GiftItemQuantity { get; set; }

// Conditions
public decimal? MinimumQuantity { get; set; }
public decimal? MinimumAmount { get; set; }
public decimal? MaximumDiscountAmount { get; set; }
```

#### Field Changes:
- `DiscountPercentage`: Changed from `decimal(18,2)` to `decimal(5,2)` for better precision

### 2. Migration Script
- **File**: `Server/PurchaseManager.Storage/Migrations/AddFrontMarginDiscountTypes.sql`
- **Features**:
  - Add new columns with appropriate data types
  - Add indexes for performance
  - Add check constraints for data integrity
  - Update existing records to DiscountType = 1
  - Add column descriptions

### 3. DTO Updates

#### Updated DTOs:
- `CreatePromotionFrontMarginDto`
- `GetPromotionFrontMarginDto`  
- `UpdatePromotionFrontMarginDto`

All DTOs now include all new fields to support 4 discount types.

### 4. Validation

#### New Validators:
- `CreatePromotionFrontMarginDtoValidator`
- `UpdatePromotionFrontMarginDtoValidator`

#### Validation Rules:
- **Type 1**: DiscountPercentage must be 0-100
- **Type 2**: FixedDiscountAmount must be > 0
- **Type 3**: BuyQuantity and GiftQuantity must be > 0
- **Type 4**: GiftItem fields must be provided and GiftItemQuantity > 0

### 5. Helper Classes

#### FrontMarginHelper
- `GetDiscountTypeName()`: Get display names
- `ValidatePromotionData()`: Validate promotion configuration
- `GetRequiredFields()`: Get required fields per type
- `IsValidConfiguration()`: Check if promotion is properly configured
- `GetPromotionDisplayText()`: Get display text for UI

#### PromotionFrontMarginExtensions
- Type checking methods: `IsPercentageDiscount()`, `IsFixedAmountDiscount()`, etc.
- Validation methods: `HasValidConfiguration()`, `IsApplicable()`
- Calculation methods: `CalculateGiftQuantity()`, etc.

## Database Constraints

### Check Constraints Added:
1. `CK_PromotionFrontMargins_DiscountType`: DiscountType must be 1,2,3,4
2. `CK_PromotionFrontMargins_PercentageDiscount`: Type 1 validation
3. `CK_PromotionFrontMargins_FixedAmount`: Type 2 validation  
4. `CK_PromotionFrontMargins_SameItemGift`: Type 3 validation
5. `CK_PromotionFrontMargins_DifferentItemGift`: Type 4 validation

### Indexes Added:
- `IX_PromotionFrontMargins_DiscountType`
- `IX_PromotionFrontMargins_GiftItemNumber`

## Discount Type Mapping

| Type | Name | Description | Required Fields |
|------|------|-------------|----------------|
| 1 | Percentage Discount | Chiết khấu theo % | DiscountPercentage |
| 2 | Fixed Amount Discount | Chiết khấu số tiền cố định | FixedDiscountAmount |
| 3 | Same Item Gift | Mua hàng tặng hàng cùng loại | BuyQuantity, GiftQuantity |
| 4 | Different Item Gift | Tặng hàng khác loại | GiftItemNumber, GiftItemName, GiftItemUOM, GiftItemQuantity |

## Business Logic Formulas

### Case I.1: Percentage Discount
```
Giá Mua = Nguyên giá × (1 - % chiết khấu)
```

### Case I.2: Fixed Amount Discount  
```
Giá Mua = Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)
```

### Case II: Same Item Gift
```
Giá Mua = Tổng giá trị theo mã hàng / Tổng số lượng theo mã hàng
VD: Mua 10 HOP tặng 1 HOP, Giá 10 triệu => Giá mua = 10 triệu/11
```

### Case III: Different Item Gift
```
Giá mua không đổi, tạo thêm gift line với giá = 0
```

## Next Steps

1. **Run Migration**: Execute `AddFrontMarginDiscountTypes.sql`
2. **Test Validation**: Test all validation rules
3. **Update UI**: Update Front Margin creation/edit forms
4. **Implement Calculators**: Create calculation logic for each type
5. **Integration Testing**: Test with existing promotion data

## Files Modified/Created

### Modified:
- `PromotionFrontMargin.cs` - Entity updated
- `CreatePromotionFrontMarginDto.cs` - DTO updated
- `GetPromotionFrontMarginDto.cs` - DTO updated  
- `UpdatePromotionFrontMarginDto.cs` - DTO updated

### Created:
- `AddFrontMarginDiscountTypes.sql` - Migration script
- `CreatePromotionFrontMarginDtoValidator.cs` - Validation
- `UpdatePromotionFrontMarginDtoValidator.cs` - Validation
- `FrontMarginHelper.cs` - Helper methods
- `PromotionFrontMarginExtensions.cs` - Extension methods
- `FrontMarginDatabaseUpdate.md` - This documentation

## Backward Compatibility

- Existing promotions will automatically have `DiscountType = 1` (Percentage)
- Existing `DiscountPercentage` values are preserved
- All new fields are nullable to maintain compatibility
- AutoMapper profiles use ReverseMap() so they automatically handle new fields
