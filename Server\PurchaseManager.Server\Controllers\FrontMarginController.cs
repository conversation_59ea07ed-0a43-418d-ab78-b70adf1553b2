using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models;
using PurchaseManager.Storage;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin CRUD operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrontMarginController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly IFrontMarginValidationService _validationService;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly ILogger<FrontMarginController> _logger;

    public FrontMarginController(
        ApplicationDbContext context,
        IMapper mapper,
        IFrontMarginValidationService validationService,
        IFrontMarginCalculationService calculationService,
        ILogger<FrontMarginController> logger)
    {
        _context = context;
        _mapper = mapper;
        _validationService = validationService;
        _calculationService = calculationService;
        _logger = logger;
    }

    /// <summary>
    /// Get all Front Margin promotions
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<GetPromotionFrontMarginDto>>>> GetAllAsync(
        [FromQuery] string? programNumber = null,
        [FromQuery] string? itemNumber = null,
        [FromQuery] int? discountType = null,
        [FromQuery] int? status = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var query = _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .Where(p => p.ModificationStatus == 1) // Active records only
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(programNumber))
                query = query.Where(p => p.ProgramNumber.Contains(programNumber));

            if (!string.IsNullOrEmpty(itemNumber))
                query = query.Where(p => p.ItemNumber.Contains(itemNumber));

            if (discountType.HasValue)
                query = query.Where(p => p.DiscountType == discountType.Value);

            if (status.HasValue)
                query = query.Where(p => p.Status == status.Value);

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var entities = await query
                .OrderBy(p => p.ProgramNumber)
                .ThenBy(p => p.LineNumber)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map using AutoMapper
            var mappedItems = _mapper.Map<List<GetPromotionFrontMarginDto>>(entities);

            var response = new ApiResponse<List<GetPromotionFrontMarginDto>>
            {
                Success = true,
                Message = $"Retrieved {mappedItems.Count} Front Margin promotions",
                Data = mappedItems,
                TotalRecords = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving Front Margin promotions");
            return StatusCode(500, ApiResponse<List<GetPromotionFrontMarginDto>>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Get Front Margin by number
    /// </summary>
    [HttpGet("{number}")]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> GetByNumberAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .FirstOrDefaultAsync(p => p.Number == number && p.ModificationStatus == 1);

            if (entity == null)
                return NotFound(ApiResponse<GetPromotionFrontMarginDto>.Error("Front Margin not found"));

            var mappedItem = _mapper.Map<GetPromotionFrontMarginDto>(entity);
            return Ok(ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin retrieved successfully", mappedItem));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Get Front Margin lines by program number
    /// </summary>
    [HttpGet("by-program/{programNumber}")]
    public async Task<ActionResult<ApiResponse<List<GetPromotionFrontMarginDto>>>> GetByProgramNumberAsync(string programNumber)
    {
        try
        {
            var entities = await _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .Where(p => p.ProgramNumber == programNumber && p.ModificationStatus == 1)
                .OrderBy(p => p.LineNumber)
                .ToListAsync();

            var mappedItems = _mapper.Map<List<GetPromotionFrontMarginDto>>(entities);
            return Ok(ApiResponse<List<GetPromotionFrontMarginDto>>.Success($"Retrieved {mappedItems.Count} Front Margin lines", mappedItems));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving Front Margin lines for program {ProgramNumber}", programNumber);
            return StatusCode(500, ApiResponse<List<GetPromotionFrontMarginDto>>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Create Front Margin
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> CreateAsync(CreatePromotionFrontMarginDto dto)
    {
        try
        {
            // Validate DTO
            var validationErrors = _validationService.ValidateDto(dto);
            if (validationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Validation failed", validationErrors));
            }

            // Check if promotion header exists
            var headerExists = await _context.PromotionHeaders
                .AnyAsync(h => h.ProgramNumber == dto.ProgramNumber && h.ModificationStatus == 1);

            if (!headerExists)
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Promotion header not found"));
            }

            // Check if line number already exists for this program
            var lineExists = await _context.PromotionFrontMargins
                .AnyAsync(p => p.ProgramNumber == dto.ProgramNumber && p.LineNumber == dto.LineNumber && p.ModificationStatus == 1);

            if (lineExists)
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error($"Line number {dto.LineNumber} already exists for program {dto.ProgramNumber}"));
            }

            // Map DTO to Entity
            var entity = _mapper.Map<PromotionFrontMargin>(dto);

            // Generate number and set tracking fields
            entity.Number = await GenerateNumberAsync();
            entity.CreatedBy = "SYSTEM"; // TODO: Get from current user
            entity.CreatedAt = DateTime.UtcNow;
            entity.ModificationStatus = 1;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Entity validation failed", entityValidationErrors));
            }

            // Save to database
            _context.PromotionFrontMargins.Add(entity);
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Created Front Margin {Number} for program {ProgramNumber}", 
                entity.Number, entity.ProgramNumber);

            return CreatedAtAction(nameof(GetByNumberAsync), new { number = entity.Number },
                ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin created successfully", result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Front Margin");
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Update Front Margin
    /// </summary>
    [HttpPut("{number}")]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> UpdateAsync(
        string number, 
        UpdatePromotionFrontMarginDto dto)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number && p.ModificationStatus == 1);

            if (entity == null)
                return NotFound(ApiResponse<GetPromotionFrontMarginDto>.Error("Front Margin not found"));

            // Validate DTO
            var validationErrors = _validationService.ValidateUpdateDto(dto);
            if (validationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Validation failed", validationErrors));
            }

            // Map DTO to Entity
            _mapper.Map(dto, entity);

            // Update tracking fields
            entity.LastModifiedBy = "SYSTEM"; // TODO: Get from current user
            entity.LastModifiedAt = DateTime.UtcNow;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Entity validation failed", entityValidationErrors));
            }

            // Save changes
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Updated Front Margin {Number}", number);

            return Ok(ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin updated successfully", result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Delete Front Margin (soft delete)
    /// </summary>
    [HttpDelete("{number}")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number && p.ModificationStatus == 1);

            if (entity == null)
                return NotFound(ApiResponse<object>.Error("Front Margin not found"));

            // Soft delete
            entity.ModificationStatus = 0; // Deleted
            entity.LastModifiedBy = "SYSTEM"; // TODO: Get from current user
            entity.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted Front Margin {Number}", number);

            return Ok(ApiResponse<object>.Success("Front Margin deleted successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Get discount types for dropdown
    /// </summary>
    [HttpGet("discount-types")]
    public ActionResult<ApiResponse<List<object>>> GetDiscountTypes()
    {
        var discountTypes = new List<object>
        {
            new { Value = 1, Text = "Chiết khấu theo phần trăm", Description = "Giá Mua = Nguyên giá × (1 - % chiết khấu)" },
            new { Value = 2, Text = "Chiết khấu số tiền cố định", Description = "Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)" },
            new { Value = 3, Text = "Mua hàng tặng hàng cùng loại", Description = "Giá Mua = Tổng giá trị / Tổng số lượng" },
            new { Value = 4, Text = "Tặng hàng khác loại", Description = "Tặng sản phẩm khác (không ảnh hưởng giá mua)" }
        };

        return Ok(ApiResponse<List<object>>.Success("Discount types retrieved successfully", discountTypes));
    }

    /// <summary>
    /// Validate Front Margin configuration
    /// </summary>
    [HttpPost("validate")]
    public ActionResult<ApiResponse<object>> ValidateConfiguration(CreatePromotionFrontMarginDto dto)
    {
        try
        {
            var validationErrors = _validationService.ValidateDto(dto);

            var response = new ApiResponse<object>
            {
                Success = !validationErrors.Any(),
                Message = validationErrors.Any() ? "Validation failed" : "Validation passed",
                Data = new { Errors = validationErrors, IsValid = !validationErrors.Any() }
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Front Margin configuration");
            return StatusCode(500, ApiResponse<object>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Generate unique number for Front Margin
    /// </summary>
    private async Task<string> GenerateNumberAsync()
    {
        var prefix = "FM";
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;

        var lastNumber = await _context.PromotionFrontMargins
            .Where(p => p.Number.StartsWith($"{prefix}{year:D4}{month:D2}"))
            .OrderByDescending(p => p.Number)
            .Select(p => p.Number)
            .FirstOrDefaultAsync();

        int sequence = 1;
        if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length >= 10)
        {
            var lastSequence = lastNumber.Substring(8);
            if (int.TryParse(lastSequence, out int parsed))
            {
                sequence = parsed + 1;
            }
        }

        return $"{prefix}{year:D4}{month:D2}{sequence:D4}";
    }
}
