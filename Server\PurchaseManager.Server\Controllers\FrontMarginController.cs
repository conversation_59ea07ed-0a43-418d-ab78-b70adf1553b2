using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models;
using PurchaseManager.Storage;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin operations using AutoMapper
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrontMarginController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IFrontMarginMappingService _mappingService;
    private readonly ILogger<FrontMarginController> _logger;

    public FrontMarginController(
        ApplicationDbContext context,
        IFrontMarginMappingService mappingService,
        ILogger<FrontMarginController> logger)
    {
        _context = context;
        _mappingService = mappingService;
        _logger = logger;
    }

    /// <summary>
    /// Get all Front Margin promotions with AutoMapper
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<GetPromotionFrontMarginDto>>>> GetAllAsync(
        [FromQuery] string? programNumber = null,
        [FromQuery] int? discountType = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var query = _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(programNumber))
                query = query.Where(p => p.ProgramNumber.Contains(programNumber));

            if (discountType.HasValue)
                query = query.Where(p => p.DiscountType == discountType.Value);

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var entities = await query
                .OrderBy(p => p.ProgramNumber)
                .ThenBy(p => p.LineNumber)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map using AutoMapper service
            var (mappedItems, _) = _mappingService.MapWithPagination(entities, totalCount);

            var response = new ApiResponse<List<GetPromotionFrontMarginDto>>
            {
                Success = true,
                Message = $"Retrieved {mappedItems.Count} Front Margin promotions",
                Data = mappedItems,
                TotalRecords = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving Front Margin promotions");
            return StatusCode(500, ApiResponse<List<GetPromotionFrontMarginDto>>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Get Front Margin by number with AutoMapper
    /// </summary>
    [HttpGet("{number}")]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> GetByNumberAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .FirstOrDefaultAsync(p => p.Number == number);

            if (entity == null)
                return NotFound(ApiResponse<GetPromotionFrontMarginDto>.Error("Front Margin not found"));

            // Map using AutoMapper service
            var mappedItem = _mappingService.MapToGetDto(entity);

            return Ok(ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin retrieved successfully", mappedItem));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Create Front Margin with AutoMapper validation
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> CreateAsync(CreatePromotionFrontMarginDto dto)
    {
        try
        {
            // Map and validate using AutoMapper service
            var (entity, validationErrors) = _mappingService.MapAndValidate(dto);

            if (validationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Validation failed", validationErrors));
            }

            // Check if promotion header exists
            var headerExists = await _context.PromotionHeaders
                .AnyAsync(h => h.ProgramNumber == dto.ProgramNumber);

            if (!headerExists)
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Promotion header not found"));
            }

            // Generate number for new entity
            entity.Number = await GenerateNumberAsync();
            entity.CreatedBy = "SYSTEM"; // TODO: Get from current user
            entity.CreatedAt = DateTime.UtcNow;
            entity.ModificationStatus = 1;

            // Save to database
            _context.PromotionFrontMargins.Add(entity);
            await _context.SaveChangesAsync();

            // Map result using AutoMapper service
            var result = _mappingService.MapToGetDto(entity);

            _logger.LogInformation("Created Front Margin {Number} for program {ProgramNumber}", 
                entity.Number, entity.ProgramNumber);

            return CreatedAtAction(nameof(GetByNumberAsync), new { number = entity.Number },
                ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin created successfully", result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Front Margin");
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Update Front Margin with AutoMapper validation
    /// </summary>
    [HttpPut("{number}")]
    public async Task<ActionResult<ApiResponse<GetPromotionFrontMarginDto>>> UpdateAsync(
        string number, 
        UpdatePromotionFrontMarginDto dto)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number);

            if (entity == null)
                return NotFound(ApiResponse<GetPromotionFrontMarginDto>.Error("Front Margin not found"));

            // Update and validate using AutoMapper service
            var validationErrors = _mappingService.UpdateAndValidate(entity, dto);

            if (validationErrors.Any())
            {
                return BadRequest(ApiResponse<GetPromotionFrontMarginDto>.Error("Validation failed", validationErrors));
            }

            // Update tracking fields
            entity.LastModifiedBy = "SYSTEM"; // TODO: Get from current user
            entity.LastModifiedAt = DateTime.UtcNow;

            // Save changes
            await _context.SaveChangesAsync();

            // Map result using AutoMapper service
            var result = _mappingService.MapToGetDto(entity);

            _logger.LogInformation("Updated Front Margin {Number}", number);

            return Ok(ApiResponse<GetPromotionFrontMarginDto>.Success("Front Margin updated successfully", result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<GetPromotionFrontMarginDto>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Delete Front Margin (soft delete)
    /// </summary>
    [HttpDelete("{number}")]
    public async Task<ActionResult<ApiResponse<object>>> DeleteAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number);

            if (entity == null)
                return NotFound(ApiResponse<object>.Error("Front Margin not found"));

            // Soft delete
            entity.ModificationStatus = 0; // Deleted
            entity.LastModifiedBy = "SYSTEM"; // TODO: Get from current user
            entity.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted Front Margin {Number}", number);

            return Ok(ApiResponse<object>.Success("Front Margin deleted successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting Front Margin {Number}", number);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Get Front Margin discount types for dropdown
    /// </summary>
    [HttpGet("discount-types")]
    public ActionResult<ApiResponse<List<object>>> GetDiscountTypes()
    {
        var discountTypes = new List<object>
        {
            new { Value = 1, Text = "Chiết khấu theo phần trăm" },
            new { Value = 2, Text = "Chiết khấu số tiền cố định" },
            new { Value = 3, Text = "Mua hàng tặng hàng cùng loại" },
            new { Value = 4, Text = "Tặng hàng khác loại" }
        };

        return Ok(ApiResponse<List<object>>.Success("Discount types retrieved successfully", discountTypes));
    }

    /// <summary>
    /// Validate Front Margin configuration
    /// </summary>
    [HttpPost("validate")]
    public ActionResult<ApiResponse<List<string>>> ValidateConfiguration(CreatePromotionFrontMarginDto dto)
    {
        try
        {
            var (entity, validationErrors) = _mappingService.MapAndValidate(dto);

            var response = new ApiResponse<List<string>>
            {
                Success = !validationErrors.Any(),
                Message = validationErrors.Any() ? "Validation failed" : "Validation passed",
                Data = validationErrors
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Front Margin configuration");
            return StatusCode(500, ApiResponse<List<string>>.Error("Internal server error"));
        }
    }

    /// <summary>
    /// Generate unique number for Front Margin
    /// </summary>
    private async Task<string> GenerateNumberAsync()
    {
        var prefix = "FM";
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;

        var lastNumber = await _context.PromotionFrontMargins
            .Where(p => p.Number.StartsWith($"{prefix}{year:D4}{month:D2}"))
            .OrderByDescending(p => p.Number)
            .Select(p => p.Number)
            .FirstOrDefaultAsync();

        int sequence = 1;
        if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length >= 10)
        {
            var lastSequence = lastNumber.Substring(8);
            if (int.TryParse(lastSequence, out int parsed))
            {
                sequence = parsed + 1;
            }
        }

        return $"{prefix}{year:D4}{month:D2}{sequence:D4}";
    }
}
