using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Managers;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Promotions;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrontMarginController : ControllerBase
{
    private readonly IFrontMarginManager _frontMarginManager;
    private readonly ApiResponse _invalidData;

    public FrontMarginController(IFrontMarginManager frontMarginManager, IStringLocalizer<Global> i18N)
    {
        _frontMarginManager = frontMarginManager;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    [HttpGet("filtered")]
    public async Task<ApiResponse> GetFrontMarginsFiltered([FromQuery] FrontMarginFilter filter)
    {
        return await _frontMarginManager.GetAllFrontMarginsAsync(filter);
    }

    [HttpGet("{number}")]
    public async Task<ApiResponse> GetFrontMarginByNumber(string number)
    {
        return await _frontMarginManager.GetFrontMarginByNumberAsync(number);
    }

    [HttpPost]
    public async Task<ApiResponse> CreateFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.CreateFrontMarginAsync(createDto) : _invalidData;
    }

    [HttpPut("{number}")]
    public async Task<ApiResponse> UpdateFrontMargin(string number, [FromBody] UpdatePromotionFrontMarginDto updateDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.UpdateFrontMarginAsync(number, updateDto) : _invalidData;
    }

    [HttpDelete("bulk")]
    public async Task<ApiResponse> DeleteFrontMargins([FromBody] List<string> numbers)
    {
        return await _frontMarginManager.DeleteFrontMarginAsync(numbers);
    }

    [HttpGet("discount-types")]
    public async Task<ApiResponse> GetDiscountTypes()
    {
        return await _frontMarginManager.GetDiscountTypesAsync();
    }

    [HttpPost("validate")]
    public async Task<ApiResponse> ValidateFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.ValidateFrontMarginAsync(createDto) : _invalidData;
    }
}
