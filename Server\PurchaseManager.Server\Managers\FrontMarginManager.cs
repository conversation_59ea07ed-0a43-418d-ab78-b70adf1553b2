using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models.Promotions;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public interface IFrontMarginManager
{
    Task<ApiResponse> GetAllFrontMarginsAsync(FrontMarginFilter filter);
    Task<ApiResponse> GetFrontMarginByNumberAsync(string number);
    Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> DeleteFrontMarginAsync(List<string> numbers);
    Task<ApiResponse> GetDiscountTypesAsync();
    Task<ApiResponse> ValidateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
}
public class FrontMarginManager : IFrontMarginManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly ILogger<FrontMarginManager> _logger;
    private readonly IFrontMarginValidationService _validationService;
    private readonly IAdminManager _adminManager;

    public FrontMarginManager(
        ApplicationDbContext context,
        IMapper mapper,
        ILogger<FrontMarginManager> logger,
        IFrontMarginValidationService validationService,
        IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _logger = logger;
        _validationService = validationService;
        _adminManager = adminManager;
    }

    public async Task<ApiResponse> GetAllFrontMarginsAsync(FrontMarginFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;

            var query = _context.PromotionFrontMargins.AsNoTracking()
                .Include(x => x.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .Where(p =>
                    p.ModificationStatus != (int)ModificationStatusEnum.DELETED &&
                    (filter.ProgramNumber == null || p.ProgramNumber.Contains(filter.ProgramNumber)) &&
                    (filter.ItemNumber == null || p.ItemNumber.Contains(filter.ItemNumber)) &&
                    (filter.ItemName == null || p.ItemName.Contains(filter.ItemName)) &&
                    (filter.VendorCode == null || p.PromotionHeader.VendorCode.Contains(filter.VendorCode)) &&
                    (filter.DiscountType == null || p.DiscountType == filter.DiscountType) &&
                    (filter.Status == null || p.Status == filter.Status) &&
                    (filter.Description == null || p.Notes.Contains(filter.Description))
                );

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var frontMargins = await query
                .OrderByDescending(p => p.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var frontMarginDtos = _mapper.Map<List<GetPromotionFrontMarginDto>>(frontMargins);
            var pagedResult = new PagedResult<GetPromotionFrontMarginDto>
            {
                Data = frontMarginDtos, CurrentPage = filter.PageIndex ?? 0, PageSize = filter.PageSize ?? 10, RowCount = totalCount
            };

            return ApiResponse.S200("Front Margins retrieved successfully", pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all front margins with filter");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetFrontMarginByNumberAsync(string number)
    {
        try
        {
            var frontMargin = await _context.PromotionFrontMargins
                .AsNoTracking()
                .Include(p => p.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .FirstOrDefaultAsync(p => p.Number == number &&
                                          p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (frontMargin == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            var frontMarginDto = _mapper.Map<GetPromotionFrontMarginDto>(frontMargin);
            return ApiResponse.S200("Front Margin retrieved successfully", frontMarginDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving front margin by number: {Number}", number);
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        try
        {
            // Validate DTO
            var validationErrors = _validationService.ValidateDto(createDto);
            if (validationErrors.Count != 0)
            {
                return ApiResponse.S400("Validation failed", validationErrors);
            }

            // Check if promotion header exists
            var headerExists = await _context.PromotionHeaders
                .AnyAsync(h => h.Number == createDto.ProgramNumber &&
                               h.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (!headerExists)
            {
                return ApiResponse.S400("Promotion header not found");
            }

            // Generate number
            const string business = "FRONTMARGIN";
            const string branch = "AL";
            var number = await _adminManager.CreateNumberSeries(business, branch);

            // Map DTO to Entity
            var entity = _mapper.Map<PromotionFrontMargin>(createDto);
            entity.Number = number;
            entity.CreatedBy = "SYSTEM";// TODO: Get from current user
            entity.CreatedAt = DateTime.UtcNow;
            entity.ModificationStatus = (int)ModificationStatusEnum.ACTIVE;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Count != 0)
            {
                return ApiResponse.S400("Entity validation failed", entityValidationErrors);
            }

            // Save to database
            _context.PromotionFrontMargins.Add(entity);
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Created Front Margin {Number} for program {ProgramNumber}",
            entity.Number, entity.ProgramNumber);

            return ApiResponse.S200("Front Margin created successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating front margin");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number &&
                                          p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (entity == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            // Validate DTO
            var validationErrors = _validationService.ValidateUpdateDto(updateDto);
            if (validationErrors.Any())
            {
                return ApiResponse.S400("Validation failed", validationErrors);
            }

            // Map DTO to Entity
            _mapper.Map(updateDto, entity);

            // Update tracking fields
            entity.LastModifiedBy = "SYSTEM";// TODO: Get from current user
            entity.LastModifiedAt = DateTime.UtcNow;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Any())
            {
                return ApiResponse.S400("Entity validation failed", entityValidationErrors);
            }

            // Save changes
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Updated Front Margin {Number}", number);

            return ApiResponse.S200("Front Margin updated successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating front margin {Number}", number);
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> DeleteFrontMarginAsync(List<string> numbers)
    {
        try
        {
            if (numbers == null || numbers.Count == 0)
            {
                return ApiResponse.S400("No front margin numbers provided");
            }

            var frontMargins = await _context.PromotionFrontMargins
                .Where(p => numbers.Contains(p.Number) &&
                            p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .ToListAsync();

            if (frontMargins.Count == 0)
            {
                return ApiResponse.S404("No front margins found with the provided numbers");
            }

            var foundNumbers = frontMargins.Select(p => p.Number)
                .ToList();
            var notFoundNumbers = numbers.Except(foundNumbers)
                .ToList();

            // Soft delete
            foreach (var frontMargin in frontMargins)
            {
                frontMargin.ModificationStatus = (int)ModificationStatusEnum.DELETED;
                frontMargin.LastModifiedBy = "SYSTEM";// TODO: Get from current user
                frontMargin.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            var result = new
            {
                DeletedCount = frontMargins.Count, DeletedNumbers = foundNumbers, NotFoundNumbers = notFoundNumbers
            };

            _logger.LogInformation("Deleted {Count} Front Margins: {Numbers}",
            frontMargins.Count, string.Join(", ", foundNumbers));

            return ApiResponse.S200("Front Margins deleted successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting front margins");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetDiscountTypesAsync()
    {
        try
        {
            var discountTypes = new List<object>
            {
                new
                {
                    Value = 1, Text = "Chiết khấu theo phần trăm", Description = "Giá Mua = Nguyên giá × (1 - % chiết khấu)"
                },
                new
                {
                    Value = 2,
                    Text = "Chiết khấu số tiền cố định",
                    Description = "Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)"
                },
                new
                {
                    Value = 3, Text = "Mua hàng tặng hàng cùng loại", Description = "Giá Mua = Tổng giá trị / Tổng số lượng"
                },
                new
                {
                    Value = 4, Text = "Tặng hàng khác loại", Description = "Tặng sản phẩm khác (không ảnh hưởng giá mua)"
                }
            };

            return ApiResponse.S200("Discount types retrieved successfully", discountTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting discount types");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> ValidateFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        try
        {
            var validationErrors = _validationService.ValidateDto(createDto);

            var result = new
            {
                IsValid = !validationErrors.Any(), Errors = validationErrors
            };

            var message = validationErrors.Any() ? "Validation failed" : "Validation passed";
            return ApiResponse.S200(message, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating front margin configuration");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }
}
