using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models.Promotions;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public interface IFrontMarginManager
{
    Task<ApiResponse> GetAllFrontMarginsAsync(FrontMarginFilter filter);
    Task<ApiResponse> GetFrontMarginByNumberAsync(string number);
    Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> DeleteFrontMarginAsync(List<string> numbers);
    Task<ApiResponse> GetDiscountTypesAsync();
    Task<ApiResponse> ValidateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> GetFrontMarginsByProgramAsync(string programNumber);
    Task<ApiResponse> DuplicateFrontMarginAsync(string number, string newProgramNumber);
    Task<ApiResponse> GetFrontMarginSummaryAsync(string? vendorCode = null);
    Task<ApiResponse> ExportFrontMarginsAsync(FrontMarginFilter filter);
    Task<ApiResponse> ImportFrontMarginsAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy);
    Task<ApiResponse> ActivateFrontMarginAsync(string number);
    Task<ApiResponse> DeactivateFrontMarginAsync(string number);
    Task<ApiResponse> GetConflictingPromotionsAsync(string itemNumber, string vendorCode);
}
public class FrontMarginManager : IFrontMarginManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly ILogger<FrontMarginManager> _logger;
    private readonly IFrontMarginValidationService _validationService;
    private readonly IAdminManager _adminManager;

    public FrontMarginManager(
        ApplicationDbContext context,
        IMapper mapper,
        ILogger<FrontMarginManager> logger,
        IFrontMarginValidationService validationService,
        IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _logger = logger;
        _validationService = validationService;
        _adminManager = adminManager;
    }

    public async Task<ApiResponse> GetAllFrontMarginsAsync(FrontMarginFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;

            var query = _context.PromotionFrontMargins.AsNoTracking()
                .Include(x => x.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .Where(p =>
                    p.ModificationStatus != (int)ModificationStatusEnum.DELETED &&
                    (filter.ProgramNumber == null || p.ProgramNumber.Contains(filter.ProgramNumber)) &&
                    (filter.ItemNumber == null || p.ItemNumber.Contains(filter.ItemNumber)) &&
                    (filter.ItemName == null || p.ItemName.Contains(filter.ItemName)) &&
                    (filter.VendorCode == null || p.PromotionHeader.VendorCode.Contains(filter.VendorCode)) &&
                    (filter.DiscountType == null || p.DiscountType == filter.DiscountType) &&
                    (filter.Status == null || p.Status == filter.Status) &&
                    (filter.Description == null || p.Notes.Contains(filter.Description))
                );

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var frontMargins = await query
                .OrderByDescending(p => p.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var frontMarginDtos = _mapper.Map<List<GetPromotionFrontMarginDto>>(frontMargins);
            var pagedResult = new PagedResult<GetPromotionFrontMarginDto>
            {
                Data = frontMarginDtos,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                RowCount = totalCount
            };

            return ApiResponse.S200("Front Margins retrieved successfully", pagedResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all front margins with filter");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetFrontMarginByNumberAsync(string number)
    {
        try
        {
            var frontMargin = await _context.PromotionFrontMargins
                .AsNoTracking()
                .Include(p => p.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .FirstOrDefaultAsync(p => p.Number == number &&
                                          p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (frontMargin == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            var frontMarginDto = _mapper.Map<GetPromotionFrontMarginDto>(frontMargin);
            return ApiResponse.S200("Front Margin retrieved successfully", frontMarginDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving front margin by number: {Number}", number);
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        try
        {
            // Validate DTO
            var validationErrors = _validationService.ValidateDto(createDto);
            if (validationErrors.Count != 0)
            {
                return ApiResponse.S400("Validation failed", validationErrors);
            }

            // Check if promotion header exists
            var headerExists = await _context.PromotionHeaders
                .AnyAsync(h => h.Number == createDto.ProgramNumber &&
                               h.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (!headerExists)
            {
                return ApiResponse.S400("Promotion header not found");
            }

            // Generate number
            const string business = "FRONTMARGIN";
            const string branch = "AL";
            var number = await _adminManager.CreateNumberSeries(business, branch);

            // Map DTO to Entity
            var entity = _mapper.Map<PromotionFrontMargin>(createDto);
            entity.Number = number;
            entity.CreatedBy = _adminManager.GetUserLogin();
            entity.CreatedAt = DateTime.UtcNow;
            entity.ModificationStatus = (int)ModificationStatusEnum.ACTIVE;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Count != 0)
            {
                return ApiResponse.S400("Entity validation failed", entityValidationErrors);
            }

            // Save to database
            _context.PromotionFrontMargins.Add(entity);
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Created Front Margin {Number} for program {ProgramNumber}",
            entity.Number, entity.ProgramNumber);

            return ApiResponse.S200("Front Margin created successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating front margin");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number &&
                                          p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (entity == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            // Validate DTO
            var validationErrors = _validationService.ValidateUpdateDto(updateDto);
            if (validationErrors.Any())
            {
                return ApiResponse.S400("Validation failed", validationErrors);
            }

            // Map DTO to Entity
            _mapper.Map(updateDto, entity);

            // Update tracking fields
            entity.LastModifiedBy = _adminManager.GetUserLogin();
            entity.LastModifiedAt = DateTime.UtcNow;

            // Validate entity
            var entityValidationErrors = _validationService.ValidateEntity(entity);
            if (entityValidationErrors.Any())
            {
                return ApiResponse.S400("Entity validation failed", entityValidationErrors);
            }

            // Save changes
            await _context.SaveChangesAsync();

            // Map result
            var result = _mapper.Map<GetPromotionFrontMarginDto>(entity);

            _logger.LogInformation("Updated Front Margin {Number}", number);

            return ApiResponse.S200("Front Margin updated successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating front margin {Number}", number);
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> DeleteFrontMarginAsync(List<string> numbers)
    {
        try
        {
            if (numbers == null || numbers.Count == 0)
            {
                return ApiResponse.S400("No front margin numbers provided");
            }

            var frontMargins = await _context.PromotionFrontMargins
                .Where(p => numbers.Contains(p.Number) &&
                            p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .ToListAsync();

            if (frontMargins.Count == 0)
            {
                return ApiResponse.S404("No front margins found with the provided numbers");
            }

            var foundNumbers = frontMargins.Select(p => p.Number)
                .ToList();
            var notFoundNumbers = numbers.Except(foundNumbers)
                .ToList();

            // Soft delete
            foreach (var frontMargin in frontMargins)
            {
                frontMargin.ModificationStatus = (int)ModificationStatusEnum.DELETED;
                frontMargin.LastModifiedBy = _adminManager.GetUserLogin();
                frontMargin.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();

            var result = new
            {
                DeletedCount = frontMargins.Count,
                DeletedNumbers = foundNumbers,
                NotFoundNumbers = notFoundNumbers
            };

            _logger.LogInformation("Deleted {Count} Front Margins: {Numbers}",
            frontMargins.Count, string.Join(", ", foundNumbers));

            return ApiResponse.S200("Front Margins deleted successfully", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting front margins");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetDiscountTypesAsync()
    {
        try
        {
            var discountTypes = new List<object>
            {
                new
                {
                    Value = 1, Text = "Chiết khấu theo phần trăm", Description = "Giá Mua = Nguyên giá × (1 - % chiết khấu)"
                },
                new
                {
                    Value = 2,
                    Text = "Chiết khấu số tiền cố định",
                    Description = "Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)"
                },
                new
                {
                    Value = 3, Text = "Mua hàng tặng hàng cùng loại", Description = "Giá Mua = Tổng giá trị / Tổng số lượng"
                },
                new
                {
                    Value = 4, Text = "Tặng hàng khác loại", Description = "Tặng sản phẩm khác (không ảnh hưởng giá mua)"
                }
            };

            return ApiResponse.S200("Discount types retrieved successfully", discountTypes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting discount types");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> ValidateFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        try
        {
            var validationErrors = _validationService.ValidateDto(createDto);

            var result = new
            {
                IsValid = !validationErrors.Any(),
                Errors = validationErrors
            };

            var message = validationErrors.Any() ? "Validation failed" : "Validation passed";
            return ApiResponse.S200(message, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating front margin configuration");
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> GetFrontMarginsByProgramAsync(string programNumber)
    {
        try
        {
            var frontMargins = await _context.PromotionFrontMargins
                .AsNoTracking()
                .Include(p => p.PromotionHeader)
                .Where(p => p.ProgramNumber == programNumber &&
                           p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .OrderBy(p => p.LineNumber)
                .ToListAsync();

            var frontMarginDtos = _mapper.Map<List<GetPromotionFrontMarginDto>>(frontMargins);

            return ApiResponse.S200($"Retrieved {frontMarginDtos.Count} Front Margin lines for program {programNumber}", frontMarginDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margins by program {ProgramNumber}", programNumber);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> DuplicateFrontMarginAsync(string number, string newProgramNumber)
    {
        try
        {
            var originalFrontMargin = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number &&
                                        p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (originalFrontMargin == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            // Check if target program exists
            var targetProgram = await _context.PromotionHeaders
                .AnyAsync(h => h.ProgramNumber == newProgramNumber &&
                              h.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (!targetProgram)
            {
                return ApiResponse.S400("Target promotion program not found");
            }

            // Find next available line number in target program
            var maxLineNumber = await _context.PromotionFrontMargins
                .Where(p => p.ProgramNumber == newProgramNumber &&
                           p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .MaxAsync(p => (int?)p.LineNumber) ?? 0;

            // Create duplicate
            var duplicateDto = _mapper.Map<CreatePromotionFrontMarginDto>(originalFrontMargin);
            duplicateDto.ProgramNumber = newProgramNumber;
            duplicateDto.LineNumber = maxLineNumber + 1;

            return await CreateFrontMarginAsync(duplicateDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error duplicating Front Margin {Number}", number);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> GetFrontMarginSummaryAsync(string? vendorCode = null)
    {
        try
        {
            var query = _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .Where(p => p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (!string.IsNullOrEmpty(vendorCode))
            {
                query = query.Where(p => p.PromotionHeader.VendorCode == vendorCode);
            }

            var summary = new
            {
                TotalFrontMargins = await query.CountAsync(),
                ByStatus = await query
                    .GroupBy(p => p.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync(),
                ByDiscountType = await query
                    .GroupBy(p => p.DiscountType)
                    .Select(g => new { DiscountType = g.Key, Count = g.Count() })
                    .ToListAsync(),
                ByVendor = await query
                    .GroupBy(p => p.PromotionHeader.VendorCode)
                    .Select(g => new { VendorCode = g.Key, Count = g.Count() })
                    .Take(10)
                    .ToListAsync(),
                RecentlyCreated = await query
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(5)
                    .Select(p => new { p.Number, p.ItemName, p.CreatedAt })
                    .ToListAsync()
            };

            return ApiResponse.S200("Front Margin summary retrieved successfully", summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin summary");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> ExportFrontMarginsAsync(FrontMarginFilter filter)
    {
        try
        {
            var query = _context.PromotionFrontMargins.AsNoTracking()
                .Include(x => x.PromotionHeader)
                .ThenInclude(h => h.Vendor)
                .Where(p => p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            // Apply filters (same as GetAllFrontMarginsAsync)
            if (!string.IsNullOrEmpty(filter.ProgramNumber))
                query = query.Where(p => p.ProgramNumber.Contains(filter.ProgramNumber));

            if (!string.IsNullOrEmpty(filter.ItemNumber))
                query = query.Where(p => p.ItemNumber.Contains(filter.ItemNumber));

            if (filter.DiscountType.HasValue)
                query = query.Where(p => p.DiscountType == filter.DiscountType);

            var frontMargins = await query
                .OrderBy(p => p.ProgramNumber)
                .ThenBy(p => p.LineNumber)
                .ToListAsync();

            var exportData = frontMargins.Select(p => new
            {
                p.Number,
                p.ProgramNumber,
                ProgramName = p.PromotionHeader?.ProgramName,
                VendorCode = p.PromotionHeader?.VendorCode,
                p.LineNumber,
                p.ItemNumber,
                p.ItemName,
                p.UnitOfMeasure,
                DiscountType = p.DiscountType,
                DiscountTypeName = GetDiscountTypeName(p.DiscountType),
                p.DiscountPercentage,
                p.FixedDiscountAmount,
                p.BuyQuantity,
                p.GiftQuantity,
                p.GiftItemNumber,
                p.GiftItemName,
                p.MinimumQuantity,
                p.MinimumAmount,
                p.MaximumDiscountAmount,
                Status = GetStatusName(p.Status),
                p.CreatedBy,
                p.CreatedAt
            }).ToList();

            return ApiResponse.S200($"Exported {exportData.Count} Front Margin records", exportData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting Front Margins");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> ImportFrontMarginsAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy)
    {
        try
        {
            var results = new List<object>();
            var successCount = 0;
            var errorCount = 0;

            foreach (var dto in dtos)
            {
                var result = await CreateFrontMarginAsync(dto);

                if (result.Success)
                {
                    successCount++;
                    results.Add(new { dto.ItemNumber, Status = "Success", Data = result.Data });
                }
                else
                {
                    errorCount++;
                    results.Add(new { dto.ItemNumber, Status = "Error", Message = result.Message });
                }
            }

            return ApiResponse.S200($"Import completed: {successCount} success, {errorCount} errors", new
            {
                TotalItems = dtos.Count,
                SuccessCount = successCount,
                ErrorCount = errorCount,
                Results = results
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing Front Margins");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> ActivateFrontMarginAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number &&
                                        p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (entity == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            entity.Status = 2; // Active
            entity.LastModifiedBy = _adminManager.GetUserLogin();
            entity.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Activated Front Margin {Number}", number);
            return ApiResponse.S200("Front Margin activated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating Front Margin {Number}", number);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> DeactivateFrontMarginAsync(string number)
    {
        try
        {
            var entity = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(p => p.Number == number &&
                                        p.ModificationStatus != (int)ModificationStatusEnum.DELETED);

            if (entity == null)
            {
                return ApiResponse.S404("Front Margin not found");
            }

            entity.Status = 3; // Inactive
            entity.LastModifiedBy = _adminManager.GetUserLogin();
            entity.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Deactivated Front Margin {Number}", number);
            return ApiResponse.S200("Front Margin deactivated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating Front Margin {Number}", number);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> GetConflictingPromotionsAsync(string itemNumber, string vendorCode)
    {
        try
        {
            var conflictingPromotions = await _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .Where(p => p.ItemNumber == itemNumber &&
                           p.PromotionHeader.VendorCode == vendorCode &&
                           p.Status == 2 && // Active
                           p.ModificationStatus != (int)ModificationStatusEnum.DELETED)
                .Select(p => new
                {
                    p.Number,
                    p.ProgramNumber,
                    ProgramName = p.PromotionHeader.ProgramName,
                    p.DiscountType,
                    DiscountTypeName = GetDiscountTypeName(p.DiscountType),
                    p.DiscountPercentage,
                    p.FixedDiscountAmount,
                    StartDate = p.PromotionHeader.StartDate,
                    EndDate = p.PromotionHeader.EndDate
                })
                .ToListAsync();

            return ApiResponse.S200($"Found {conflictingPromotions.Count} conflicting promotions", conflictingPromotions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting conflicting promotions for item {ItemNumber}", itemNumber);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    #region Helper Methods

    private string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    private string GetStatusName(int status)
    {
        return status switch
        {
            1 => "Draft",
            2 => "Active",
            3 => "Inactive",
            4 => "Expired",
            _ => "Unknown"
        };
    }

    #endregion
}
