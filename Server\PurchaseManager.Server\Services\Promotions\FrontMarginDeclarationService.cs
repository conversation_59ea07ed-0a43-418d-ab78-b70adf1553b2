using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Storage;

namespace PurchaseManager.Server.Services.Promotions;

/// <summary>
/// Service for Front Margin declaration and registration workflow
/// </summary>
public interface IFrontMarginDeclarationService
{
    /// <summary>
    /// Validate Front Margin declaration before saving
    /// </summary>
    Task<FrontMarginDeclarationResult> ValidateDeclarationAsync(CreatePromotionFrontMarginDto dto);
    
    /// <summary>
    /// Register Front Margin for approval workflow
    /// </summary>
    Task<FrontMarginDeclarationResult> RegisterForApprovalAsync(CreatePromotionFrontMarginDto dto, string createdBy);
    
    /// <summary>
    /// Bulk register multiple Front Margins
    /// </summary>
    Task<FrontMarginBulkDeclarationResult> BulkRegisterAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy);
    
    /// <summary>
    /// Get declaration status and workflow information
    /// </summary>
    Task<FrontMarginDeclarationStatus> GetDeclarationStatusAsync(string number);
    
    /// <summary>
    /// Check for conflicts with existing promotions
    /// </summary>
    Task<List<FrontMarginConflict>> CheckConflictsAsync(CreatePromotionFrontMarginDto dto);
    
    /// <summary>
    /// Preview calculation impact before registration
    /// </summary>
    Task<FrontMarginImpactPreview> PreviewImpactAsync(CreatePromotionFrontMarginDto dto);
}

public class FrontMarginDeclarationService : IFrontMarginDeclarationService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly IFrontMarginValidationService _validationService;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly ILogger<FrontMarginDeclarationService> _logger;

    public FrontMarginDeclarationService(
        ApplicationDbContext context,
        IMapper mapper,
        IFrontMarginValidationService validationService,
        IFrontMarginCalculationService calculationService,
        ILogger<FrontMarginDeclarationService> logger)
    {
        _context = context;
        _mapper = mapper;
        _validationService = validationService;
        _calculationService = calculationService;
        _logger = logger;
    }

    public async Task<FrontMarginDeclarationResult> ValidateDeclarationAsync(CreatePromotionFrontMarginDto dto)
    {
        var result = new FrontMarginDeclarationResult
        {
            IsValid = true,
            ValidationErrors = new List<string>(),
            Warnings = new List<string>()
        };

        try
        {
            // Basic DTO validation
            var validationErrors = _validationService.ValidateDto(dto);
            result.ValidationErrors.AddRange(validationErrors);

            if (validationErrors.Any())
            {
                result.IsValid = false;
                return result;
            }

            // Check if promotion header exists and is valid
            var header = await _context.PromotionHeaders
                .FirstOrDefaultAsync(h => h.ProgramNumber == dto.ProgramNumber && 
                                        h.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

            if (header == null)
            {
                result.ValidationErrors.Add("Chương trình khuyến mãi không tồn tại hoặc đã bị xóa");
                result.IsValid = false;
                return result;
            }

            // Check if header is in correct status for adding Front Margin
            if (header.Status != 1) // Draft status
            {
                result.Warnings.Add("Chương trình khuyến mãi không ở trạng thái Draft. Có thể cần approval để thay đổi.");
            }

            // Check date validity
            if (header.StartDate > DateTime.Now.AddDays(365))
            {
                result.Warnings.Add("Ngày bắt đầu chương trình còn xa (>1 năm). Vui lòng kiểm tra lại.");
            }

            if (header.EndDate < DateTime.Now)
            {
                result.ValidationErrors.Add("Chương trình khuyến mãi đã hết hạn");
                result.IsValid = false;
            }

            // Check for duplicate line number
            var existingLine = await _context.PromotionFrontMargins
                .AnyAsync(p => p.ProgramNumber == dto.ProgramNumber && 
                              p.LineNumber == dto.LineNumber && 
                              p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

            if (existingLine)
            {
                result.ValidationErrors.Add($"Số dòng {dto.LineNumber} đã tồn tại trong chương trình {dto.ProgramNumber}");
                result.IsValid = false;
            }

            // Check for conflicts with existing promotions
            var conflicts = await CheckConflictsAsync(dto);
            if (conflicts.Any())
            {
                result.Conflicts = conflicts;
                result.Warnings.Add($"Phát hiện {conflicts.Count} xung đột với các khuyến mãi khác");
            }

            // Business rule validations
            await ValidateBusinessRulesAsync(dto, result);

            result.HeaderInfo = new PromotionHeaderInfo
            {
                ProgramNumber = header.ProgramNumber,
                ProgramName = header.ProgramName,
                VendorCode = header.VendorCode,
                StartDate = header.StartDate,
                EndDate = header.EndDate,
                Status = header.Status
            };

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Front Margin declaration");
            result.ValidationErrors.Add("Lỗi hệ thống khi validate. Vui lòng thử lại.");
            result.IsValid = false;
        }

        return result;
    }

    public async Task<FrontMarginDeclarationResult> RegisterForApprovalAsync(CreatePromotionFrontMarginDto dto, string createdBy)
    {
        var validationResult = await ValidateDeclarationAsync(dto);
        
        if (!validationResult.IsValid)
        {
            return validationResult;
        }

        try
        {
            // Map to entity
            var entity = _mapper.Map<PromotionFrontMargin>(dto);
            
            // Generate number
            entity.Number = await GenerateNumberAsync();
            entity.CreatedBy = createdBy;
            entity.CreatedAt = DateTime.UtcNow;
            entity.ModificationStatus = (int)ModificationStatusEnum.ACTIVE;
            
            // Set initial status based on business rules
            entity.Status = await DetermineInitialStatusAsync(dto);

            // Save to database
            _context.PromotionFrontMargins.Add(entity);
            await _context.SaveChangesAsync();

            validationResult.CreatedNumber = entity.Number;
            validationResult.InitialStatus = entity.Status;
            validationResult.RequiresApproval = entity.Status == 1; // Draft status

            _logger.LogInformation("Registered Front Margin {Number} for program {ProgramNumber} by {CreatedBy}", 
                entity.Number, entity.ProgramNumber, createdBy);

            return validationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering Front Margin for approval");
            validationResult.ValidationErrors.Add("Lỗi hệ thống khi đăng ký. Vui lòng thử lại.");
            validationResult.IsValid = false;
            return validationResult;
        }
    }

    public async Task<FrontMarginBulkDeclarationResult> BulkRegisterAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy)
    {
        var result = new FrontMarginBulkDeclarationResult
        {
            TotalItems = dtos.Count,
            SuccessfulItems = new List<FrontMarginDeclarationResult>(),
            FailedItems = new List<FrontMarginDeclarationResult>()
        };

        foreach (var dto in dtos)
        {
            var itemResult = await RegisterForApprovalAsync(dto, createdBy);
            
            if (itemResult.IsValid)
            {
                result.SuccessfulItems.Add(itemResult);
            }
            else
            {
                result.FailedItems.Add(itemResult);
            }
        }

        result.SuccessCount = result.SuccessfulItems.Count;
        result.FailureCount = result.FailedItems.Count;

        _logger.LogInformation("Bulk registration completed: {Success}/{Total} successful", 
            result.SuccessCount, result.TotalItems);

        return result;
    }

    public async Task<FrontMarginDeclarationStatus> GetDeclarationStatusAsync(string number)
    {
        var frontMargin = await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .FirstOrDefaultAsync(p => p.Number == number);

        if (frontMargin == null)
        {
            return new FrontMarginDeclarationStatus
            {
                Number = number,
                Status = "NOT_FOUND",
                StatusDescription = "Không tìm thấy khai báo Front Margin"
            };
        }

        return new FrontMarginDeclarationStatus
        {
            Number = frontMargin.Number,
            ProgramNumber = frontMargin.ProgramNumber,
            Status = GetStatusName(frontMargin.Status),
            StatusDescription = GetStatusDescription(frontMargin.Status),
            CreatedBy = frontMargin.CreatedBy,
            CreatedAt = frontMargin.CreatedAt,
            LastModifiedBy = frontMargin.LastModifiedBy,
            LastModifiedAt = frontMargin.LastModifiedAt,
            CanEdit = CanEdit(frontMargin.Status),
            CanDelete = CanDelete(frontMargin.Status),
            RequiresApproval = RequiresApproval(frontMargin.Status)
        };
    }

    public async Task<List<FrontMarginConflict>> CheckConflictsAsync(CreatePromotionFrontMarginDto dto)
    {
        var conflicts = new List<FrontMarginConflict>();

        // Check for overlapping promotions for the same item
        var overlappingPromotions = await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .Where(p => p.ItemNumber == dto.ItemNumber &&
                       p.PromotionHeader.VendorCode == dto.ProgramNumber && // Assuming vendor context
                       p.Status == 2 && // Active
                       p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE)
            .ToListAsync();

        foreach (var existing in overlappingPromotions)
        {
            conflicts.Add(new FrontMarginConflict
            {
                ConflictType = "OVERLAPPING_PROMOTION",
                ConflictingNumber = existing.Number,
                ConflictingProgramNumber = existing.ProgramNumber,
                Description = $"Sản phẩm {dto.ItemNumber} đã có khuyến mãi trong chương trình {existing.ProgramNumber}",
                Severity = "WARNING",
                Resolution = "Có thể cần điều chỉnh thời gian hoặc điều kiện áp dụng"
            });
        }

        return conflicts;
    }

    public async Task<FrontMarginImpactPreview> PreviewImpactAsync(CreatePromotionFrontMarginDto dto)
    {
        // This would calculate potential impact on margins, costs, etc.
        // For now, return a basic preview
        return new FrontMarginImpactPreview
        {
            EstimatedImpact = "Cần implement logic tính toán impact",
            AffectedItems = new List<string> { dto.ItemNumber },
            EstimatedSavings = 0,
            RiskLevel = "LOW"
        };
    }

    #region Private Methods

    private async Task ValidateBusinessRulesAsync(CreatePromotionFrontMarginDto dto, FrontMarginDeclarationResult result)
    {
        // Business rule: Check minimum quantities make sense
        if (dto.MinimumQuantity.HasValue && dto.MinimumQuantity.Value > 10000)
        {
            result.Warnings.Add("Số lượng tối thiểu rất cao (>10,000). Vui lòng kiểm tra lại.");
        }

        // Business rule: Check discount percentages
        if (dto.DiscountType == 1 && dto.DiscountPercentage > 50)
        {
            result.Warnings.Add("Tỷ lệ chiết khấu cao (>50%). Có thể cần approval đặc biệt.");
        }

        // Business rule: Check fixed amounts
        if (dto.DiscountType == 2 && dto.FixedDiscountAmount.HasValue && dto.FixedDiscountAmount.Value > 1000000)
        {
            result.Warnings.Add("Số tiền chiết khấu cao (>1M). Có thể cần approval đặc biệt.");
        }
    }

    private async Task<int> DetermineInitialStatusAsync(CreatePromotionFrontMarginDto dto)
    {
        // Business logic to determine if Front Margin needs approval
        // For now, all new Front Margins start as Draft (1)
        return 1; // Draft
    }

    private async Task<string> GenerateNumberAsync()
    {
        var prefix = "FM";
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;

        var lastNumber = await _context.PromotionFrontMargins
            .Where(p => p.Number.StartsWith($"{prefix}{year:D4}{month:D2}"))
            .OrderByDescending(p => p.Number)
            .Select(p => p.Number)
            .FirstOrDefaultAsync();

        int sequence = 1;
        if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length >= 10)
        {
            var lastSequence = lastNumber.Substring(8);
            if (int.TryParse(lastSequence, out int parsed))
            {
                sequence = parsed + 1;
            }
        }

        return $"{prefix}{year:D4}{month:D2}{sequence:D4}";
    }

    private string GetStatusName(int status)
    {
        return status switch
        {
            1 => "DRAFT",
            2 => "ACTIVE",
            3 => "INACTIVE",
            4 => "EXPIRED",
            _ => "UNKNOWN"
        };
    }

    private string GetStatusDescription(int status)
    {
        return status switch
        {
            1 => "Bản nháp - Chờ kích hoạt",
            2 => "Đang hoạt động",
            3 => "Tạm ngưng",
            4 => "Đã hết hạn",
            _ => "Không xác định"
        };
    }

    private bool CanEdit(int status) => status == 1; // Only draft can be edited
    private bool CanDelete(int status) => status == 1; // Only draft can be deleted
    private bool RequiresApproval(int status) => status == 1; // Draft requires approval

    #endregion
}

#region Result Classes

public class FrontMarginDeclarationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<FrontMarginConflict> Conflicts { get; set; } = new();
    public string? CreatedNumber { get; set; }
    public int InitialStatus { get; set; }
    public bool RequiresApproval { get; set; }
    public PromotionHeaderInfo? HeaderInfo { get; set; }
}

public class FrontMarginBulkDeclarationResult
{
    public int TotalItems { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<FrontMarginDeclarationResult> SuccessfulItems { get; set; } = new();
    public List<FrontMarginDeclarationResult> FailedItems { get; set; } = new();
}

public class FrontMarginDeclarationStatus
{
    public string Number { get; set; } = string.Empty;
    public string? ProgramNumber { get; set; }
    public string Status { get; set; } = string.Empty;
    public string StatusDescription { get; set; } = string.Empty;
    public string? CreatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? LastModifiedBy { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
    public bool RequiresApproval { get; set; }
}

public class FrontMarginConflict
{
    public string ConflictType { get; set; } = string.Empty;
    public string ConflictingNumber { get; set; } = string.Empty;
    public string ConflictingProgramNumber { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty; // LOW, MEDIUM, HIGH
    public string Resolution { get; set; } = string.Empty;
}

public class FrontMarginImpactPreview
{
    public string EstimatedImpact { get; set; } = string.Empty;
    public List<string> AffectedItems { get; set; } = new();
    public decimal EstimatedSavings { get; set; }
    public string RiskLevel { get; set; } = string.Empty;
}

public class PromotionHeaderInfo
{
    public string ProgramNumber { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Status { get; set; }
}

#endregion
