using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Storage.Mapping.Extensions;

namespace PurchaseManager.Server.Services.Promotions;

/// <summary>
/// Service for Front Margin AutoMapper operations
/// </summary>
public interface IFrontMarginMappingService
{
    /// <summary>
    /// Map entity to GetDto with all helper properties
    /// </summary>
    GetPromotionFrontMarginDto MapToGetDto(PromotionFrontMargin entity);
    
    /// <summary>
    /// Map multiple entities to GetDtos
    /// </summary>
    List<GetPromotionFrontMarginDto> MapToGetDtos(IEnumerable<PromotionFrontMargin> entities);
    
    /// <summary>
    /// Map CreateDto to entity with validation
    /// </summary>
    PromotionFrontMargin MapToEntity(CreatePromotionFrontMarginDto dto);
    
    /// <summary>
    /// Update entity from UpdateDto with validation
    /// </summary>
    void UpdateEntity(PromotionFrontMargin entity, UpdatePromotionFrontMarginDto dto);
    
    /// <summary>
    /// Validate entity configuration
    /// </summary>
    List<string> ValidateEntity(PromotionFrontMargin entity);
}

public class FrontMarginMappingService : IFrontMarginMappingService
{
    private readonly IMapper _mapper;

    public FrontMarginMappingService(IMapper mapper)
    {
        _mapper = mapper;
    }

    public GetPromotionFrontMarginDto MapToGetDto(PromotionFrontMargin entity)
    {
        return entity.ToGetDto(_mapper);
    }

    public List<GetPromotionFrontMarginDto> MapToGetDtos(IEnumerable<PromotionFrontMargin> entities)
    {
        return entities.Select(e => e.ToGetDto(_mapper)).ToList();
    }

    public PromotionFrontMargin MapToEntity(CreatePromotionFrontMarginDto dto)
    {
        return dto.ToEntity(_mapper);
    }

    public void UpdateEntity(PromotionFrontMargin entity, UpdatePromotionFrontMarginDto dto)
    {
        entity.UpdateFromDto(dto, _mapper);
    }

    public List<string> ValidateEntity(PromotionFrontMargin entity)
    {
        return entity.ValidateConfiguration();
    }
}

/// <summary>
/// Extension methods for easier usage in controllers/managers
/// </summary>
public static class FrontMarginMappingServiceExtensions
{
    /// <summary>
    /// Map and validate CreateDto to entity
    /// </summary>
    public static (PromotionFrontMargin entity, List<string> errors) MapAndValidate(
        this IFrontMarginMappingService service, 
        CreatePromotionFrontMarginDto dto)
    {
        var entity = service.MapToEntity(dto);
        var errors = service.ValidateEntity(entity);
        return (entity, errors);
    }

    /// <summary>
    /// Update and validate entity from UpdateDto
    /// </summary>
    public static List<string> UpdateAndValidate(
        this IFrontMarginMappingService service,
        PromotionFrontMargin entity,
        UpdatePromotionFrontMarginDto dto)
    {
        service.UpdateEntity(entity, dto);
        return service.ValidateEntity(entity);
    }

    /// <summary>
    /// Map entities with pagination info
    /// </summary>
    public static (List<GetPromotionFrontMarginDto> items, int totalCount) MapWithPagination(
        this IFrontMarginMappingService service,
        IEnumerable<PromotionFrontMargin> entities,
        int totalCount)
    {
        var mappedItems = service.MapToGetDtos(entities);
        return (mappedItems, totalCount);
    }
}

/// <summary>
/// AutoMapper configuration helper for Front Margin
/// </summary>
public static class FrontMarginAutoMapperConfig
{
    /// <summary>
    /// Configure AutoMapper for Front Margin with custom logic
    /// </summary>
    public static void ConfigureFrontMarginMappings(this IMapperConfigurationExpression cfg)
    {
        // Entity to GetDto with computed properties
        cfg.CreateMap<PromotionFrontMargin, GetPromotionFrontMarginDto>()
            .ForMember(dest => dest.DiscountTypeName, opt => opt.MapFrom(src => GetDiscountTypeName(src.DiscountType)))
            .ForMember(dest => dest.IsPercentageDiscount, opt => opt.MapFrom(src => src.DiscountType == 1))
            .ForMember(dest => dest.IsFixedAmountDiscount, opt => opt.MapFrom(src => src.DiscountType == 2))
            .ForMember(dest => dest.IsSameItemGift, opt => opt.MapFrom(src => src.DiscountType == 3))
            .ForMember(dest => dest.IsDifferentItemGift, opt => opt.MapFrom(src => src.DiscountType == 4));

        // CreateDto to Entity with field clearing
        cfg.CreateMap<CreatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ForMember(dest => dest.Number, opt => opt.Ignore())
            .ForMember(dest => dest.PromotionHeader, opt => opt.Ignore())
            .AfterMap((src, dest) => ClearInvalidFieldsForDiscountType(dest));

        // UpdateDto to Entity with field clearing
        cfg.CreateMap<UpdatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ForMember(dest => dest.Number, opt => opt.Ignore())
            .ForMember(dest => dest.ProgramNumber, opt => opt.Ignore())
            .ForMember(dest => dest.LineNumber, opt => opt.Ignore())
            .ForMember(dest => dest.PromotionHeader, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RowId, opt => opt.Ignore())
            .AfterMap((src, dest) => ClearInvalidFieldsForDiscountType(dest));
    }

    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại", 
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    private static void ClearInvalidFieldsForDiscountType(PromotionFrontMargin entity)
    {
        switch (entity.DiscountType)
        {
            case 1: // Percentage
                entity.FixedDiscountAmount = null;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 2: // Fixed Amount
                entity.DiscountPercentage = 0;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 3: // Same Item Gift
                entity.DiscountPercentage = 0;
                entity.FixedDiscountAmount = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 4: // Different Item Gift
                entity.DiscountPercentage = 0;
                entity.FixedDiscountAmount = null;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                break;
        }
    }
}

/// <summary>
/// Example usage in controller/manager
/// </summary>
public static class FrontMarginMappingUsageExample
{
    /*
    // In your controller or manager:
    
    private readonly IFrontMarginMappingService _mappingService;
    
    public async Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto dto)
    {
        // Map and validate in one call
        var (entity, errors) = _mappingService.MapAndValidate(dto);
        
        if (errors.Any())
        {
            return ApiResponse.BadRequest("Validation failed", errors);
        }
        
        // Save entity
        await _context.PromotionFrontMargins.AddAsync(entity);
        await _context.SaveChangesAsync();
        
        // Return mapped result
        var result = _mappingService.MapToGetDto(entity);
        return ApiResponse.Success("Created successfully", result);
    }
    
    public async Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto dto)
    {
        var entity = await _context.PromotionFrontMargins.FindAsync(number);
        if (entity == null)
            return ApiResponse.NotFound("Front Margin not found");
        
        // Update and validate in one call
        var errors = _mappingService.UpdateAndValidate(entity, dto);
        
        if (errors.Any())
        {
            return ApiResponse.BadRequest("Validation failed", errors);
        }
        
        await _context.SaveChangesAsync();
        
        var result = _mappingService.MapToGetDto(entity);
        return ApiResponse.Success("Updated successfully", result);
    }
    */
}
