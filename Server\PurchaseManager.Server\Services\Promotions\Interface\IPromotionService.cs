using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IPromotionService
{
    Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> UpdatePromotionFrontMarginAsync(string promotionNumber, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> OpenDocument(string documentNumber);
}
