using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Storage;
using Xunit;

namespace PurchaseManager.Server.Tests;

/// <summary>
/// Comprehensive test suite for Front Margin Calculation Engine
/// Tests all 4 discount types with various scenarios and edge cases
/// </summary>
public class FrontMarginCalculationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly IMapper _mapper;

    public FrontMarginCalculationTests()
    {
        // Setup in-memory database for testing
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);

        // Setup AutoMapper
        var config = new MapperConfiguration(cfg =>
        {
            // Add your mapping profiles here
        });
        _mapper = config.CreateMapper();

        // Setup calculation service
        _calculationService = new FrontMarginCalculationService(_mapper, _context);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test promotion headers
        var headers = new List<PromotionHeader>
        {
            new PromotionHeader
            {
                ProgramNumber = "TEST_CALC_001",
                ProgramName = "Test Calculation Program",
                VendorCode = "VENDOR001",
                ProgramType = 1,
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now.AddDays(30),
                Status = 2,
                ModificationStatus = 1
            }
        };

        _context.PromotionHeaders.AddRange(headers);

        // Create test front margin promotions for all 4 types
        var frontMargins = new List<PromotionFrontMargin>
        {
            // Type 1: Percentage Discount
            new PromotionFrontMargin
            {
                Number = "FM001",
                ProgramNumber = "TEST_CALC_001",
                LineNumber = 1,
                ItemNumber = "ITEM001",
                ItemName = "Test Item 1",
                UnitOfMeasure = "PCS",
                DiscountType = 1,
                DiscountPercentage = 10.0m,
                MinimumQuantity = 10,
                MinimumAmount = 100000,
                Status = 1,
                ModificationStatus = 1
            },
            // Type 2: Fixed Amount Discount
            new PromotionFrontMargin
            {
                Number = "FM002",
                ProgramNumber = "TEST_CALC_001",
                LineNumber = 2,
                ItemNumber = "ITEM002",
                ItemName = "Test Item 2",
                UnitOfMeasure = "PCS",
                DiscountType = 2,
                FixedDiscountAmount = 50000,
                MinimumAmount = 500000,
                MaximumDiscountAmount = 100000,
                Status = 1,
                ModificationStatus = 1
            },
            // Type 3: Same Item Gift
            new PromotionFrontMargin
            {
                Number = "FM003",
                ProgramNumber = "TEST_CALC_001",
                LineNumber = 3,
                ItemNumber = "ITEM003",
                ItemName = "Test Item 3",
                UnitOfMeasure = "PCS",
                DiscountType = 3,
                BuyQuantity = 10,
                GiftQuantity = 2,
                MinimumQuantity = 10,
                Status = 1,
                ModificationStatus = 1
            },
            // Type 4: Different Item Gift
            new PromotionFrontMargin
            {
                Number = "FM004",
                ProgramNumber = "TEST_CALC_001",
                LineNumber = 4,
                ItemNumber = "ITEM004",
                ItemName = "Test Item 4",
                UnitOfMeasure = "PCS",
                DiscountType = 4,
                GiftItemNumber = "GIFT001",
                GiftItemName = "Gift Item 1",
                GiftItemUOM = "PCS",
                GiftItemQuantity = 5,
                MinimumQuantity = 50,
                Status = 1,
                ModificationStatus = 1
            }
        };

        _context.PromotionFrontMargins.AddRange(frontMargins);
        _context.SaveChanges();
    }

    #region Type 1: Percentage Discount Tests

    [Fact]
    public async Task CalculateLineDiscount_PercentageDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM001",
            Quantity = 20,
            UnitCost = 10000 // 10,000 VND per unit
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM001" && p.DiscountType == 1)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10000, result.OriginalUnitCost);
        Assert.Equal(200000, result.OriginalAmount); // 20 * 10,000
        Assert.Equal(9000, result.FinalUnitCost); // 10,000 * (1 - 0.1)
        Assert.Equal(180000, result.FinalAmount); // 20 * 9,000
        Assert.Equal(20000, result.TotalSavings); // 200,000 - 180,000
    }

    [Fact]
    public async Task CalculateLineDiscount_PercentageDiscount_BelowMinimum_ShouldNotApplyDiscount()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM001",
            Quantity = 5, // Below minimum quantity of 10
            UnitCost = 10000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM001" && p.DiscountType == 1)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.Equal(10000, result.OriginalUnitCost);
        Assert.Equal(10000, result.FinalUnitCost); // No discount applied
        Assert.Equal(0, result.TotalSavings);
    }

    #endregion

    #region Type 2: Fixed Amount Discount Tests

    [Fact]
    public async Task CalculateLineDiscount_FixedAmountDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM002",
            Quantity = 100,
            UnitCost = 8000 // Total: 800,000 VND (above minimum 500,000)
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM002" && p.DiscountType == 2)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(8000, result.OriginalUnitCost);
        Assert.Equal(800000, result.OriginalAmount);
        Assert.True(result.TotalDiscountAmount > 0);
        Assert.True(result.FinalUnitCost < result.OriginalUnitCost);
    }

    [Fact]
    public async Task CalculateLineDiscount_FixedAmountDiscount_WithMaximumLimit_ShouldRespectLimit()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM002",
            Quantity = 200,
            UnitCost = 10000 // Total: 2,000,000 VND (high amount to test max limit)
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM002" && p.DiscountType == 2)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        // Should not exceed maximum discount amount of 100,000
        Assert.True(result.TotalDiscountAmount <= 100000);
    }

    #endregion

    #region Type 3: Same Item Gift Tests

    [Fact]
    public async Task CalculateLineDiscount_SameItemGift_ShouldCalculateCorrectly()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM003",
            Quantity = 30, // Should get 6 free items (3 sets of buy 10 get 2)
            UnitCost = 5000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM003" && p.DiscountType == 3)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5000, result.OriginalUnitCost);
        Assert.Equal(150000, result.OriginalAmount); // 30 * 5,000
        Assert.True(result.FinalUnitCost < result.OriginalUnitCost); // Should be lower due to gift
        Assert.NotNull(result.GiftLines);
        Assert.Single(result.GiftLines); // Should have one gift line
        Assert.Equal(6, result.GiftLines[0].Quantity); // 6 free items
    }

    [Fact]
    public async Task CalculateLineDiscount_SameItemGift_PartialSet_ShouldCalculateCorrectly()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM003",
            Quantity = 25, // Should get 4 free items (2 complete sets)
            UnitCost = 5000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM003" && p.DiscountType == 3)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.GiftLines);
        Assert.Single(result.GiftLines);
        Assert.Equal(4, result.GiftLines[0].Quantity); // Only 4 free items for 2 complete sets
    }

    #endregion

    #region Type 4: Different Item Gift Tests

    [Fact]
    public async Task CalculateLineDiscount_DifferentItemGift_ShouldCreateGiftLine()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM004",
            Quantity = 100, // Above minimum of 50
            UnitCost = 15000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM004" && p.DiscountType == 4)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(15000, result.OriginalUnitCost);
        Assert.Equal(15000, result.FinalUnitCost); // No change to unit cost for different item gift
        Assert.Equal(0, result.TotalDiscountAmount); // No monetary discount
        Assert.NotNull(result.GiftLines);
        Assert.Single(result.GiftLines);
        Assert.Equal("GIFT001", result.GiftLines[0].ItemNumber);
        Assert.Equal(5, result.GiftLines[0].Quantity);
        Assert.Equal(0, result.GiftLines[0].UnitCost); // Gift items have zero cost
    }

    [Fact]
    public async Task CalculateLineDiscount_DifferentItemGift_BelowMinimum_ShouldNotCreateGift()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM004",
            Quantity = 30, // Below minimum of 50
            UnitCost = 15000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM004" && p.DiscountType == 4)
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(15000, result.FinalUnitCost); // No change
        Assert.True(result.GiftLines == null || !result.GiftLines.Any()); // No gift lines
    }

    #endregion

    #region Edge Cases and Error Handling

    [Fact]
    public async Task CalculateLineDiscount_NoApplicablePromotions_ShouldReturnOriginalValues()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "NONEXISTENT_ITEM",
            Quantity = 10,
            UnitCost = 5000
        };

        var promotions = new List<PromotionFrontMargin>();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5000, result.OriginalUnitCost);
        Assert.Equal(5000, result.FinalUnitCost);
        Assert.Equal(50000, result.OriginalAmount);
        Assert.Equal(50000, result.FinalAmount);
        Assert.Equal(0, result.TotalSavings);
    }

    [Fact]
    public async Task CalculateLineDiscount_ZeroQuantity_ShouldHandleGracefully()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM001",
            Quantity = 0,
            UnitCost = 10000
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM001")
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.OriginalAmount);
        Assert.Equal(0, result.FinalAmount);
    }

    [Fact]
    public async Task CalculateLineDiscount_ZeroUnitCost_ShouldHandleGracefully()
    {
        // Arrange
        var poLine = new POLineGetDto
        {
            ItemNumber = "ITEM001",
            Quantity = 10,
            UnitCost = 0
        };

        var promotions = await _context.PromotionFrontMargins
            .Where(p => p.ItemNumber == "ITEM001")
            .ToListAsync();

        // Act
        var result = await _calculationService.CalculateLineDiscountAsync(poLine, promotions);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.OriginalAmount);
        Assert.Equal(0, result.FinalAmount);
        Assert.Equal(0, result.TotalSavings);
    }

    #endregion

    #region PO Level Tests

    [Fact]
    public async Task ApplyFrontMarginToPO_MultipleLines_ShouldCalculateCorrectly()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO001",
            BuyFromVendorNumber = "VENDOR001"
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                ItemNumber = "ITEM001",
                Quantity = 20,
                UnitCost = 10000,
                Amount = 200000
            },
            new POLineGetDto
            {
                ItemNumber = "ITEM002",
                Quantity = 100,
                UnitCost = 8000,
                Amount = 800000
            }
        };

        // Act
        var result = await _calculationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1000000, result.OriginalPOAmount); // 200,000 + 800,000
        Assert.True(result.FinalPOAmount < result.OriginalPOAmount); // Should have discounts
        Assert.True(result.TotalSavings > 0);
        Assert.True(result.TotalDiscountPercentage > 0);
        Assert.NotEmpty(result.LineResults);
    }

    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
