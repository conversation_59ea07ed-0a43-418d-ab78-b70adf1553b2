using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Storage;
using Xunit;

namespace PurchaseManager.Server.Tests;

/// <summary>
/// Integration tests for Front Margin system with PO integration
/// </summary>
public class FrontMarginIntegrationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly IFrontMarginIntegrationService _integrationService;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly IFrontMarginCacheService _cacheService;
    private readonly IMapper _mapper;

    public FrontMarginIntegrationTests()
    {
        // Setup in-memory database
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new ApplicationDbContext(options);

        // Setup services
        var services = new ServiceCollection();
        services.AddLogging();
        services.AddMemoryCache();
        
        // Setup AutoMapper
        var config = new MapperConfiguration(cfg =>
        {
            // Add your mapping profiles here
        });
        _mapper = config.CreateMapper();

        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<FrontMarginIntegrationService>>();
        var cacheLogger = serviceProvider.GetRequiredService<ILogger<FrontMarginCacheService>>();
        var calcLogger = serviceProvider.GetRequiredService<ILogger<FrontMarginCalculationService>>();
        var cache = serviceProvider.GetRequiredService<Microsoft.Extensions.Caching.Memory.IMemoryCache>();

        _cacheService = new FrontMarginCacheService(_context, cache, cacheLogger);
        _calculationService = new FrontMarginCalculationService(_mapper, _context);
        _integrationService = new FrontMarginIntegrationService(_context, _calculationService, _cacheService, logger);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create test promotion header
        var header = new PromotionHeader
        {
            ProgramNumber = "INT_TEST_001",
            ProgramName = "Integration Test Program",
            VendorCode = "VENDOR001",
            ProgramType = 1,
            StartDate = DateTime.Now.AddDays(-30),
            EndDate = DateTime.Now.AddDays(30),
            Status = 2,
            ModificationStatus = 1
        };

        _context.PromotionHeaders.Add(header);

        // Create test front margin promotions
        var frontMargins = new List<PromotionFrontMargin>
        {
            // Percentage discount
            new PromotionFrontMargin
            {
                Number = "FM_INT_001",
                ProgramNumber = "INT_TEST_001",
                LineNumber = 1,
                ItemNumber = "ITEM001",
                ItemName = "Test Item 1",
                UnitOfMeasure = "PCS",
                DiscountType = 1,
                DiscountPercentage = 15.0m,
                MinimumQuantity = 10,
                Status = 2,
                ModificationStatus = 1
            },
            // Same item gift
            new PromotionFrontMargin
            {
                Number = "FM_INT_002",
                ProgramNumber = "INT_TEST_001",
                LineNumber = 2,
                ItemNumber = "ITEM002",
                ItemName = "Test Item 2",
                UnitOfMeasure = "PCS",
                DiscountType = 3,
                BuyQuantity = 10,
                GiftQuantity = 2,
                MinimumQuantity = 10,
                Status = 2,
                ModificationStatus = 1
            },
            // Different item gift
            new PromotionFrontMargin
            {
                Number = "FM_INT_003",
                ProgramNumber = "INT_TEST_001",
                LineNumber = 3,
                ItemNumber = "ITEM003",
                ItemName = "Test Item 3",
                UnitOfMeasure = "PCS",
                DiscountType = 4,
                GiftItemNumber = "GIFT001",
                GiftItemName = "Gift Item 1",
                GiftItemUOM = "PCS",
                GiftItemQuantity = 5,
                MinimumQuantity = 20,
                Status = 2,
                ModificationStatus = 1
            }
        };

        _context.PromotionFrontMargins.AddRange(frontMargins);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetApplicablePromotions_ShouldReturnCorrectPromotions()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO001",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        // Act
        var promotions = await _integrationService.GetApplicablePromotionsAsync(poHeader);

        // Assert
        Assert.NotEmpty(promotions);
        Assert.Equal(3, promotions.Count);
        Assert.All(promotions, p => Assert.Equal("VENDOR001", p.PromotionHeader.VendorCode));
    }

    [Fact]
    public async Task ApplyFrontMarginToPO_WithPercentageDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO002",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM001",
                Description = "Test Item 1",
                Quantity = 20,
                UnitCost = 10000,
                UnitOfMeasure = "PCS",
                Amount = 200000
            }
        };

        // Act
        var result = await _integrationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(200000, result.TotalOriginalAmount);
        Assert.True(result.TotalFinalAmount < result.TotalOriginalAmount);
        Assert.True(result.TotalSavings > 0);
        Assert.Single(result.UpdatedPOLines);
        Assert.Single(result.AppliedPromotions);
    }

    [Fact]
    public async Task ApplyFrontMarginToPO_WithSameItemGift_ShouldCreateGiftLines()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO003",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM002",
                Description = "Test Item 2",
                Quantity = 25, // Should get 4 free items (2 complete sets)
                UnitCost = 5000,
                UnitOfMeasure = "PCS",
                Amount = 125000
            }
        };

        // Act
        var result = await _integrationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

        // Assert
        Assert.True(result.Success);
        Assert.Single(result.UpdatedPOLines);
        Assert.Single(result.GiftLines);
        Assert.Equal(4, result.GiftLines[0].Quantity); // 2 complete sets * 2 gift each
        Assert.Equal(0, result.GiftLines[0].UnitCost); // Gift items are free
    }

    [Fact]
    public async Task ApplyFrontMarginToPO_WithDifferentItemGift_ShouldCreateDifferentGiftLine()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO004",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM003",
                Description = "Test Item 3",
                Quantity = 30, // Above minimum of 20
                UnitCost = 8000,
                UnitOfMeasure = "PCS",
                Amount = 240000
            }
        };

        // Act
        var result = await _integrationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

        // Assert
        Assert.True(result.Success);
        Assert.Single(result.UpdatedPOLines);
        Assert.Single(result.GiftLines);
        Assert.Equal("GIFT001", result.GiftLines[0].ItemNumber);
        Assert.Equal(5, result.GiftLines[0].Quantity);
        Assert.Equal(0, result.GiftLines[0].UnitCost);
    }

    [Fact]
    public async Task PreviewFrontMarginImpact_ShouldProvideAccuratePreview()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO005",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM001",
                Quantity = 15,
                UnitCost = 10000,
                Amount = 150000
            },
            new POLineGetDto
            {
                LineNumber = 2,
                ItemNumber = "ITEM002",
                Quantity = 20,
                UnitCost = 5000,
                Amount = 100000
            }
        };

        // Act
        var preview = await _integrationService.PreviewFrontMarginImpactAsync(poHeader, poLines);

        // Assert
        Assert.Equal("PO005", preview.PONumber);
        Assert.Equal("VENDOR001", preview.VendorCode);
        Assert.Equal(250000, preview.TotalOriginalAmount);
        Assert.True(preview.TotalProjectedSavings > 0);
        Assert.Equal(2, preview.LineImpacts.Count);
        Assert.Equal(2, preview.LinesWithPromotions);
    }

    [Fact]
    public async Task ValidatePOForFrontMargin_WithValidPO_ShouldReturnValid()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO006",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM001",
                Quantity = 15,
                UnitCost = 10000
            }
        };

        // Act
        var validation = await _integrationService.ValidatePOForFrontMarginAsync(poHeader, poLines);

        // Assert
        Assert.True(validation.IsValid);
        Assert.NotEmpty(validation.ValidationMessages);
    }

    [Fact]
    public async Task ValidatePOForFrontMargin_WithInvalidPO_ShouldReturnInvalid()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO007",
            BuyFromVendorNumber = "", // Invalid - empty vendor
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>();

        // Act
        var validation = await _integrationService.ValidatePOForFrontMarginAsync(poHeader, poLines);

        // Assert
        Assert.False(validation.IsValid);
        Assert.NotEmpty(validation.ValidationMessages);
    }

    [Fact]
    public async Task ApplyFrontMarginToPO_WithMultiplePromotions_ShouldHandleCorrectly()
    {
        // Arrange
        var poHeader = new POHeaderGetDto
        {
            PONumber = "PO008",
            BuyFromVendorNumber = "VENDOR001",
            OrderDate = DateTime.Now
        };

        var poLines = new List<POLineGetDto>
        {
            new POLineGetDto
            {
                LineNumber = 1,
                ItemNumber = "ITEM001", // Percentage discount
                Quantity = 15,
                UnitCost = 10000,
                Amount = 150000
            },
            new POLineGetDto
            {
                LineNumber = 2,
                ItemNumber = "ITEM002", // Same item gift
                Quantity = 20,
                UnitCost = 5000,
                Amount = 100000
            },
            new POLineGetDto
            {
                LineNumber = 3,
                ItemNumber = "ITEM003", // Different item gift
                Quantity = 25,
                UnitCost = 8000,
                Amount = 200000
            },
            new POLineGetDto
            {
                LineNumber = 4,
                ItemNumber = "ITEM999", // No promotion
                Quantity = 10,
                UnitCost = 3000,
                Amount = 30000
            }
        };

        // Act
        var result = await _integrationService.ApplyFrontMarginToPOAsync(poHeader, poLines);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(480000, result.TotalOriginalAmount);
        Assert.True(result.TotalSavings > 0);
        Assert.Equal(4, result.UpdatedPOLines.Count);
        Assert.Equal(2, result.GiftLines.Count); // From ITEM002 and ITEM003
        Assert.Equal(3, result.AppliedPromotions.Count); // ITEM001, ITEM002, ITEM003
    }

    [Fact]
    public async Task GetUsageHistory_ShouldReturnHistory()
    {
        // Arrange
        var vendorCode = "VENDOR001";
        var fromDate = DateTime.Now.AddDays(-60);
        var toDate = DateTime.Now;

        // Act
        var history = await _integrationService.GetUsageHistoryAsync(vendorCode, fromDate, toDate);

        // Assert
        Assert.NotNull(history);
        // Note: This returns sample data in the current implementation
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
