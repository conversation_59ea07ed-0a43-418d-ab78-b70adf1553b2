using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using PurchaseManager.Server.Managers;
using PurchaseManager.Shared.Dto.PO;
using System.Net.Http.Json;
using Xunit;

namespace PurchaseManager.Server.Tests;

/// <summary>
/// Integration tests for PO Front Margin integration
/// </summary>
public class POFrontMarginIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public POFrontMarginIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task ApplyFrontMarginToPO_WithValidPO_ShouldReturnSuccess()
    {
        // Arrange
        var request = new
        {
            POHeader = new POHeaderGetDto
            {
                PONumber = "TEST_PO_001",
                BuyFromVendorNumber = "VENDOR001",
                OrderDate = DateTime.Now
            },
            POLines = new List<POLineGetDto>
            {
                new POLineGetDto
                {
                    LineNumber = 1,
                    ItemNumber = "ITEM001",
                    Description = "Test Item 1",
                    Quantity = 20,
                    UnitCost = 10000,
                    UnitOfMeasure = "PCS",
                    Amount = 200000
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/apply-front-margin", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin applied successfully", content);
    }

    [Fact]
    public async Task PreviewFrontMarginImpact_WithValidPO_ShouldReturnPreview()
    {
        // Arrange
        var request = new
        {
            POHeader = new POHeaderGetDto
            {
                PONumber = "TEST_PO_002",
                BuyFromVendorNumber = "VENDOR001",
                OrderDate = DateTime.Now
            },
            POLines = new List<POLineGetDto>
            {
                new POLineGetDto
                {
                    LineNumber = 1,
                    ItemNumber = "ITEM001",
                    Quantity = 15,
                    UnitCost = 10000
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/preview-front-margin", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin impact preview generated", content);
    }

    [Fact]
    public async Task ValidatePOForFrontMargin_WithValidPO_ShouldReturnValid()
    {
        // Arrange
        var request = new
        {
            POHeader = new POHeaderGetDto
            {
                PONumber = "TEST_PO_003",
                BuyFromVendorNumber = "VENDOR001",
                OrderDate = DateTime.Now
            },
            POLines = new List<POLineGetDto>
            {
                new POLineGetDto
                {
                    LineNumber = 1,
                    ItemNumber = "ITEM001",
                    Quantity = 15,
                    UnitCost = 10000
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/validate-front-margin", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("PO validation completed", content);
    }

    [Fact]
    public async Task GetVendorFrontMarginStats_WithValidVendor_ShouldReturnStats()
    {
        // Arrange
        var vendorCode = "VENDOR001";

        // Act
        var response = await _client.GetAsync($"/api/po-integration/vendor-stats/{vendorCode}");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin statistics", content);
    }

    [Fact]
    public async Task AutoApplyFrontMargin_WithValidPO_ShouldReturnResult()
    {
        // Arrange
        var request = new
        {
            POHeader = new POHeaderGetDto
            {
                PONumber = "TEST_PO_004",
                BuyFromVendorNumber = "VENDOR001",
                OrderDate = DateTime.Now
            },
            POLines = new List<POLineGetDto>
            {
                new POLineGetDto
                {
                    LineNumber = 1,
                    ItemNumber = "ITEM001",
                    Quantity = 25,
                    UnitCost = 8000
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/auto-apply-front-margin", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        // Should either apply Front Margin or indicate auto-apply status
        Assert.True(response.IsSuccessStatusCode);
    }

    [Fact]
    public async Task GetIntegrationDashboard_ShouldReturnDashboardData()
    {
        // Act
        var response = await _client.GetAsync("/api/po-integration/dashboard");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Integration dashboard data retrieved", content);
    }

    [Fact]
    public async Task GetIntegrationHealth_ShouldReturnHealthStatus()
    {
        // Act
        var response = await _client.GetAsync("/api/po-integration/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Integration health status", content);
    }

    [Fact]
    public async Task BulkApplyFrontMargin_WithMultiplePOs_ShouldProcessAll()
    {
        // Arrange
        var requests = new[]
        {
            new
            {
                POHeader = new POHeaderGetDto
                {
                    PONumber = "BULK_PO_001",
                    BuyFromVendorNumber = "VENDOR001",
                    OrderDate = DateTime.Now
                },
                POLines = new List<POLineGetDto>
                {
                    new POLineGetDto
                    {
                        LineNumber = 1,
                        ItemNumber = "ITEM001",
                        Quantity = 20,
                        UnitCost = 10000
                    }
                }
            },
            new
            {
                POHeader = new POHeaderGetDto
                {
                    PONumber = "BULK_PO_002",
                    BuyFromVendorNumber = "VENDOR001",
                    OrderDate = DateTime.Now
                },
                POLines = new List<POLineGetDto>
                {
                    new POLineGetDto
                    {
                        LineNumber = 1,
                        ItemNumber = "ITEM002",
                        Quantity = 15,
                        UnitCost = 5000
                    }
                }
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/bulk-apply-front-margin", requests);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Bulk apply completed", content);
    }

    [Fact]
    public async Task GetVendorIntegrationSettings_WithValidVendor_ShouldReturnSettings()
    {
        // Arrange
        var vendorCode = "VENDOR001";

        // Act
        var response = await _client.GetAsync($"/api/po-integration/settings/{vendorCode}");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Integration settings", content);
    }

    [Fact]
    public async Task UpdateVendorIntegrationSettings_WithValidData_ShouldUpdateSettings()
    {
        // Arrange
        var vendorCode = "VENDOR001";
        var settings = new
        {
            AutoApplyEnabled = true,
            RequireApproval = false,
            MaxDiscountPercentage = 30.0,
            AllowGiftItems = true,
            NotificationSettings = new
            {
                EmailOnApply = false,
                EmailOnLargeDiscount = true,
                LargeDiscountThreshold = 25.0
            }
        };

        // Act
        var response = await _client.PutAsJsonAsync($"/api/po-integration/settings/{vendorCode}", settings);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Integration settings updated", content);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task ApplyFrontMarginToPO_WithInvalidPONumber_ShouldReturnBadRequest(string poNumber)
    {
        // Arrange
        var request = new
        {
            POHeader = new POHeaderGetDto
            {
                PONumber = poNumber,
                BuyFromVendorNumber = "VENDOR001",
                OrderDate = DateTime.Now
            },
            POLines = new List<POLineGetDto>()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/po-integration/apply-front-margin", request);

        // Assert
        Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task RemoveFrontMarginFromPO_WithValidPONumber_ShouldReturnSuccess()
    {
        // Arrange
        var poNumber = "TEST_PO_REMOVE";

        // Act
        var response = await _client.DeleteAsync($"/api/po-integration/remove-front-margin/{poNumber}");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin removed", content);
    }

    [Fact]
    public async Task GetPOFrontMarginSummary_WithValidPONumber_ShouldReturnSummary()
    {
        // Arrange
        var poNumber = "TEST_PO_SUMMARY";

        // Act
        var response = await _client.GetAsync($"/api/po-integration/front-margin-summary/{poNumber}");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin summary", content);
    }

    [Fact]
    public async Task RecalculateFrontMargin_WithValidPONumber_ShouldReturnSuccess()
    {
        // Arrange
        var poNumber = "TEST_PO_RECALC";

        // Act
        var response = await _client.PostAsync($"/api/po-integration/recalculate-front-margin/{poNumber}", null);

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Front Margin recalculated", content);
    }
}

/// <summary>
/// Unit tests for POFrontMarginIntegrationManager
/// </summary>
public class POFrontMarginIntegrationManagerTests
{
    [Fact]
    public void POFrontMarginIntegrationManager_ShouldImplementInterface()
    {
        // This test ensures the manager implements the required interface
        Assert.True(typeof(IPOFrontMarginIntegrationManager).IsAssignableFrom(typeof(POFrontMarginIntegrationManager)));
    }

    [Fact]
    public void POFrontMarginIntegrationManager_ShouldHaveRequiredMethods()
    {
        // Verify all required methods exist
        var managerType = typeof(POFrontMarginIntegrationManager);
        
        Assert.NotNull(managerType.GetMethod("ApplyFrontMarginToPOAsync"));
        Assert.NotNull(managerType.GetMethod("PreviewFrontMarginImpactAsync"));
        Assert.NotNull(managerType.GetMethod("ValidatePOForFrontMarginAsync"));
        Assert.NotNull(managerType.GetMethod("RemoveFrontMarginFromPOAsync"));
        Assert.NotNull(managerType.GetMethod("GetPOFrontMarginSummaryAsync"));
        Assert.NotNull(managerType.GetMethod("RecalculateFrontMarginAsync"));
        Assert.NotNull(managerType.GetMethod("GetVendorFrontMarginStatsAsync"));
        Assert.NotNull(managerType.GetMethod("AutoApplyFrontMarginAsync"));
    }
}
