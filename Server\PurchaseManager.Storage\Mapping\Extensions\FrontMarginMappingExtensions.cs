using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Constants.Enum;

namespace PurchaseManager.Storage.Mapping.Extensions;

/// <summary>
/// AutoMapper extensions for Front Margin mappings
/// </summary>
public static class FrontMarginMappingExtensions
{
    /// <summary>
    /// Map entity to DTO with discount type specific logic
    /// </summary>
    public static GetPromotionFrontMarginDto ToGetDto(this PromotionFrontMargin entity, IMapper mapper)
    {
        var dto = mapper.Map<GetPromotionFrontMarginDto>(entity);
        
        // Set helper properties based on discount type
        dto.DiscountTypeName = GetDiscountTypeName(entity.DiscountType);
        dto.IsPercentageDiscount = entity.DiscountType == 1;
        dto.IsFixedAmountDiscount = entity.DiscountType == 2;
        dto.IsSameItemGift = entity.DiscountType == 3;
        dto.IsDifferentItemGift = entity.DiscountType == 4;
        
        return dto;
    }

    /// <summary>
    /// Map DTO to entity with validation
    /// </summary>
    public static PromotionFrontMargin ToEntity(this CreatePromotionFrontMarginDto dto, IMapper mapper)
    {
        var entity = mapper.Map<PromotionFrontMargin>(dto);
        
        // Clear fields that shouldn't be set for this discount type
        ClearInvalidFieldsForDiscountType(entity);
        
        return entity;
    }

    /// <summary>
    /// Update entity from DTO with validation
    /// </summary>
    public static void UpdateFromDto(this PromotionFrontMargin entity, UpdatePromotionFrontMarginDto dto, IMapper mapper)
    {
        // Store original values that shouldn't change
        var originalNumber = entity.Number;
        var originalProgramNumber = entity.ProgramNumber;
        var originalLineNumber = entity.LineNumber;
        var originalCreatedBy = entity.CreatedBy;
        var originalCreatedAt = entity.CreatedAt;
        var originalRowId = entity.RowId;
        
        // Map the update
        mapper.Map(dto, entity);
        
        // Restore original values
        entity.Number = originalNumber;
        entity.ProgramNumber = originalProgramNumber;
        entity.LineNumber = originalLineNumber;
        entity.CreatedBy = originalCreatedBy;
        entity.CreatedAt = originalCreatedAt;
        entity.RowId = originalRowId;
        
        // Clear invalid fields for the discount type
        ClearInvalidFieldsForDiscountType(entity);
    }

    /// <summary>
    /// Clear fields that are not applicable for the discount type
    /// </summary>
    private static void ClearInvalidFieldsForDiscountType(PromotionFrontMargin entity)
    {
        switch (entity.DiscountType)
        {
            case 1: // Percentage
                entity.FixedDiscountAmount = null;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 2: // Fixed Amount
                entity.DiscountPercentage = 0;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 3: // Same Item Gift
                entity.DiscountPercentage = 0;
                entity.FixedDiscountAmount = null;
                entity.GiftItemNumber = null;
                entity.GiftItemName = null;
                entity.GiftItemUOM = null;
                entity.GiftItemQuantity = null;
                break;
                
            case 4: // Different Item Gift
                entity.DiscountPercentage = 0;
                entity.FixedDiscountAmount = null;
                entity.BuyQuantity = null;
                entity.GiftQuantity = null;
                break;
        }
    }

    /// <summary>
    /// Get display name for discount type
    /// </summary>
    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định", 
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Validate Front Margin configuration
    /// </summary>
    public static List<string> ValidateConfiguration(this PromotionFrontMargin entity)
    {
        var errors = new List<string>();

        // Common validations
        if (string.IsNullOrWhiteSpace(entity.ItemNumber))
            errors.Add("Mã sản phẩm không được để trống");

        if (string.IsNullOrWhiteSpace(entity.ItemName))
            errors.Add("Tên sản phẩm không được để trống");

        if (string.IsNullOrWhiteSpace(entity.UnitOfMeasure))
            errors.Add("Đơn vị tính không được để trống");

        // Discount type specific validations
        switch (entity.DiscountType)
        {
            case 1: // Percentage
                if (entity.DiscountPercentage <= 0 || entity.DiscountPercentage > 100)
                    errors.Add("Phần trăm chiết khấu phải từ 0.01 đến 100");
                break;

            case 2: // Fixed Amount
                if (!entity.FixedDiscountAmount.HasValue || entity.FixedDiscountAmount.Value <= 0)
                    errors.Add("Số tiền chiết khấu cố định phải lớn hơn 0");
                break;

            case 3: // Same Item Gift
                if (!entity.BuyQuantity.HasValue || entity.BuyQuantity.Value <= 0)
                    errors.Add("Số lượng mua phải lớn hơn 0");
                if (!entity.GiftQuantity.HasValue || entity.GiftQuantity.Value <= 0)
                    errors.Add("Số lượng tặng phải lớn hơn 0");
                break;

            case 4: // Different Item Gift
                if (string.IsNullOrWhiteSpace(entity.GiftItemNumber))
                    errors.Add("Mã sản phẩm tặng không được để trống");
                if (string.IsNullOrWhiteSpace(entity.GiftItemName))
                    errors.Add("Tên sản phẩm tặng không được để trống");
                if (string.IsNullOrWhiteSpace(entity.GiftItemUOM))
                    errors.Add("Đơn vị tính sản phẩm tặng không được để trống");
                if (!entity.GiftItemQuantity.HasValue || entity.GiftItemQuantity.Value <= 0)
                    errors.Add("Số lượng sản phẩm tặng phải lớn hơn 0");
                break;

            default:
                errors.Add("Loại chiết khấu không hợp lệ");
                break;
        }

        return errors;
    }

    /// <summary>
    /// Check if Front Margin is applicable for a PO line
    /// </summary>
    public static bool IsApplicableForPOLine(this PromotionFrontMargin entity, decimal quantity, decimal amount)
    {
        // Check minimum quantity
        if (entity.MinimumQuantity.HasValue && quantity < entity.MinimumQuantity.Value)
            return false;

        // Check minimum amount
        if (entity.MinimumAmount.HasValue && amount < entity.MinimumAmount.Value)
            return false;

        // Check if promotion is active
        if (entity.Status != 1)
            return false;

        return true;
    }

    /// <summary>
    /// Calculate discount amount for a PO line
    /// </summary>
    public static decimal CalculateDiscountAmount(this PromotionFrontMargin entity, decimal quantity, decimal unitCost)
    {
        if (!entity.IsApplicableForPOLine(quantity, quantity * unitCost))
            return 0;

        return entity.DiscountType switch
        {
            1 => CalculatePercentageDiscount(entity, quantity, unitCost),
            2 => CalculateFixedAmountDiscount(entity, quantity, unitCost),
            3 => CalculateSameItemGiftDiscount(entity, quantity, unitCost),
            4 => 0, // Different item gift doesn't affect unit cost
            _ => 0
        };
    }

    private static decimal CalculatePercentageDiscount(PromotionFrontMargin entity, decimal quantity, decimal unitCost)
    {
        var totalAmount = quantity * unitCost;
        var discountAmount = totalAmount * (entity.DiscountPercentage / 100);
        
        // Apply maximum discount limit if set
        if (entity.MaximumDiscountAmount.HasValue && discountAmount > entity.MaximumDiscountAmount.Value)
            discountAmount = entity.MaximumDiscountAmount.Value;
            
        return discountAmount;
    }

    private static decimal CalculateFixedAmountDiscount(PromotionFrontMargin entity, decimal quantity, decimal unitCost)
    {
        if (!entity.FixedDiscountAmount.HasValue)
            return 0;

        var discountAmount = entity.FixedDiscountAmount.Value;
        
        // Apply maximum discount limit if set
        if (entity.MaximumDiscountAmount.HasValue && discountAmount > entity.MaximumDiscountAmount.Value)
            discountAmount = entity.MaximumDiscountAmount.Value;
            
        return discountAmount;
    }

    private static decimal CalculateSameItemGiftDiscount(PromotionFrontMargin entity, decimal quantity, decimal unitCost)
    {
        if (!entity.BuyQuantity.HasValue || !entity.GiftQuantity.HasValue)
            return 0;

        var giftSets = Math.Floor(quantity / entity.BuyQuantity.Value);
        var totalGiftQuantity = giftSets * entity.GiftQuantity.Value;
        
        if (totalGiftQuantity > 0)
        {
            var totalQuantityIncludingGifts = quantity + totalGiftQuantity;
            var newUnitCost = (unitCost * quantity) / totalQuantityIncludingGifts;
            var discountAmount = (unitCost - newUnitCost) * quantity;
            
            // Apply maximum discount limit if set
            if (entity.MaximumDiscountAmount.HasValue && discountAmount > entity.MaximumDiscountAmount.Value)
                discountAmount = entity.MaximumDiscountAmount.Value;
                
            return discountAmount;
        }

        return 0;
    }
}
