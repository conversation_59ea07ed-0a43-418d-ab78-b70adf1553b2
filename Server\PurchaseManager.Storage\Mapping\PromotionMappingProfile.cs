﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;

namespace PurchaseManager.Storage.Mapping;

public class PromotionMappingProfile : Profile
{
    public PromotionMappingProfile()
    {
        CreateMap<PromotionHeader, GetPromotionHeaderDto>()
            .ForMember(destinationMember: dest => dest.ProgramCode, memberOptions: opt => opt.MapFrom(src => src.Number))
            .ForMember(destinationMember: dest => dest.VendorName,
            memberOptions: opt => opt.MapFrom(src => src.Vendor != null ? src.Vendor.Name : string.Empty))
            .ForMember(destinationMember: dest => dest.FrontMargins,
            memberOptions: opt => opt.MapFrom(src => src.PromotionFrontMargins))
            .ReverseMap()
            .ForMember(dest => dest.Number, opt => opt.MapFrom(src => src.ProgramCode))
            .ForMember(destinationMember: dest => dest.Vendor, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionFrontMargins,
            memberOptions: opt => opt.MapFrom(src => src.FrontMargins));

        CreateMap<CreatePromotionHeaderDto, PromotionHeader>()
            .ForMember(destinationMember: dest => dest.Vendor, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.PromotionFrontMargins, memberOptions: opt => opt.Ignore())
            .ForMember(dest => dest.PromotionConditions, opt => opt.Ignore())
            .ForMember(dest => dest.PromotionRewards, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<PromotionHeader, UpdatePromotionHeaderDto>()
            .ReverseMap();

        CreateMap<GetPromotionHeaderDto, UpdatePromotionHeaderDto>()
            .ReverseMap();
        
        CreateMap<PromotionFrontMargin, GetPromotionFrontMarginDto>()
            .ReverseMap();

        CreateMap<CreatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ReverseMap();

        CreateMap<GetPromotionFrontMarginDto, CreatePromotionFrontMarginDto>()
            .ReverseMap();

        CreateMap<UpdateFrontMarginPromotionDto, PromotionFrontMargin>()
            .ReverseMap();

        CreateMap<UpdatePromotionFrontMarginDto, PromotionFrontMargin>()
            .ReverseMap();

        CreateMap<UpdatePromotionFrontMarginDto, GetPromotionFrontMarginDto>()
            .ReverseMap();
    }
}
