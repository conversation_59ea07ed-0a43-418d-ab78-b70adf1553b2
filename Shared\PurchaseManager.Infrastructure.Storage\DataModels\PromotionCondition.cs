using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Promotion Conditions - <PERSON><PERSON><PERSON><PERSON> kiện khuyến mãi
/// </summary>
[Table("PromotionConditions")]
public class PromotionCondition : EntityBase
{
    [Required]
    [StringLength(50)]
    public string PromotionNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// ItemName - Always lookup from Item table
    /// </summary>
    [StringLength(500)]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Số lượng điều kiện
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal Quantity { get; set; }

    /// <summary>
    /// Unit - Always lookup from Item table
    /// </summary>
    [StringLength(20)]
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// % Chiết khấu điều kiện (FrontMargin)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal DiscountPercent { get; set; }

    /// <summary>
    /// Minimum purchase amount/quantity (BackMargin: doanh số/số lượng tối thiểu)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MinAmount { get; set; }

    /// <summary>
    /// Maximum purchase amount/quantity (BackMargin: doanh số/số lượng tối đa)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxAmount { get; set; }

    /// <summary>
    /// Milestone amount/quantity (BackMargin: doanh số/số lượng mốc)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MilestoneAmount { get; set; }

    /// <summary>
    /// Condition Type: 1=FrontMargin, 2=BackMarginRevenue, 3=BackMarginQuantity, 4=BackMarginEarlyPayment
    /// </summary>
    public int ConditionType { get; set; } = 1;

    /// <summary>
    /// Early payment days (BackMargin only)
    /// </summary>
    public int? EarlyPaymentDays { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation properties
    [ForeignKey("PromotionNumber")]
    public virtual PromotionHeader PromotionHeader { get; set; } = null!;
}
