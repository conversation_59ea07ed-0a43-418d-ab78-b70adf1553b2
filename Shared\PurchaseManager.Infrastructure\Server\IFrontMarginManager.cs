using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models.Promotions;
namespace PurchaseManager.Infrastructure.Server;

public interface IFrontMarginManager
{
    Task<ApiResponse> GetAllFrontMarginsAsync(FrontMarginFilter filter);
    Task<ApiResponse> GetFrontMarginByNumberAsync(string number);
    Task<ApiResponse> CreateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> UpdateFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> DeleteFrontMarginAsync(List<string> numbers);
    Task<ApiResponse> GetDiscountTypesAsync();
    Task<ApiResponse> ValidateFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> GetFrontMarginsByProgramAsync(string programNumber);
    Task<ApiResponse> DuplicateFrontMarginAsync(string number, string newProgramNumber);
    Task<ApiResponse> GetFrontMarginSummaryAsync(string? vendorCode = null);
    Task<ApiResponse> ExportFrontMarginsAsync(FrontMarginFilter filter);
    Task<ApiResponse> ImportFrontMarginsAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy);
    Task<ApiResponse> ActivateFrontMarginAsync(string number);
    Task<ApiResponse> DeactivateFrontMarginAsync(string number);
    Task<ApiResponse> GetConflictingPromotionsAsync(string itemNumber, string vendorCode);
}
