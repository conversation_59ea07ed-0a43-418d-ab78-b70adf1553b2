﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class CreatePromotionFrontMarginDto
{
    public string ProgramNumber { get; set; } = null!;

    public string ItemNumber { get; set; } = null!;

    public string ItemName { get; set; } = null!;

    public string UnitOfMeasure { get; set; } = null!;

    /// <summary>
    /// Loại chiết khấu: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; } = 1;

    // CASE I.1: Percentage Discount
    public decimal DiscountPercentage { get; set; } = 0;

    // CASE I.2: Fixed Amount Discount
    public decimal? FixedDiscountAmount { get; set; }

    // CASE II: Same Item Gift (Buy X Get Y Free)
    public decimal? BuyQuantity { get; set; }
    public decimal? GiftQuantity { get; set; }

    // CASE III: Different Item Gift
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public string? GiftItemUOM { get; set; }
    public decimal? GiftItemQuantity { get; set; }

    // Conditions
    public decimal? MinimumQuantity { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }

    public string? Notes { get; set; }
}
