using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Storage.DataModels;

namespace PurchaseManager.Shared.Helpers;

public static class BackMarginHelper
{
    /// <summary>
    /// Create Back Margin condition for revenue-based discount
    /// </summary>
    public static PromotionCondition CreateRevenueCondition(
        string promotionNumber, 
        string itemNumber, 
        string itemName,
        string unitOfMeasure,
        BackMarginDiscountTypeEnum discountType,
        decimal? minRevenue = null,
        decimal? maxRevenue = null,
        decimal? milestoneRevenue = null)
    {
        return new PromotionCondition
        {
            PromotionNumber = promotionNumber,
            ItemNumber = itemNumber,
            ItemName = itemName,
            UnitOfMeasure = unitOfMeasure,
            ConditionType = discountType == BackMarginDiscountTypeEnum.RevenueProgressive 
                ? (int)PromotionConditionTypeEnum.BackMarginRevenue
                : (int)PromotionConditionTypeEnum.BackMarginRevenue,
            MinAmount = minRevenue,
            MaxAmount = maxRevenue,
            MilestoneAmount = milestoneRevenue,
            Quantity = 0 // Not used for revenue-based
        };
    }

    /// <summary>
    /// Create Back Margin condition for quantity-based discount
    /// </summary>
    public static PromotionCondition CreateQuantityCondition(
        string promotionNumber,
        string itemNumber,
        string itemName,
        string unitOfMeasure,
        BackMarginDiscountTypeEnum discountType,
        decimal? minQuantity = null,
        decimal? maxQuantity = null,
        decimal? milestoneQuantity = null)
    {
        return new PromotionCondition
        {
            PromotionNumber = promotionNumber,
            ItemNumber = itemNumber,
            ItemName = itemName,
            UnitOfMeasure = unitOfMeasure,
            ConditionType = (int)PromotionConditionTypeEnum.BackMarginQuantity,
            MinAmount = minQuantity,
            MaxAmount = maxQuantity,
            MilestoneAmount = milestoneQuantity,
            Quantity = milestoneQuantity ?? 0
        };
    }

    /// <summary>
    /// Create Back Margin condition for early payment discount
    /// </summary>
    public static PromotionCondition CreateEarlyPaymentCondition(
        string promotionNumber,
        string itemNumber,
        string itemName,
        int earlyPaymentDays)
    {
        return new PromotionCondition
        {
            PromotionNumber = promotionNumber,
            ItemNumber = itemNumber,
            ItemName = itemName,
            UnitOfMeasure = "DAYS",
            ConditionType = (int)PromotionConditionTypeEnum.BackMarginEarlyPayment,
            EarlyPaymentDays = earlyPaymentDays,
            Quantity = earlyPaymentDays
        };
    }

    /// <summary>
    /// Create Back Margin reward
    /// </summary>
    public static PromotionReward CreateBackMarginReward(
        string promotionNumber,
        string conditionItemNumber,
        PromotionRewardTypeEnum rewardType,
        DiscountValueTypeEnum discountValueType,
        BackMarginCalculationTypeEnum calculationType,
        decimal? discountPercent = null,
        decimal? discountAmount = null,
        int? tierLevel = null,
        decimal? maxRewardAmount = null)
    {
        return new PromotionReward
        {
            PromotionNumber = promotionNumber,
            ConditionItemNumber = conditionItemNumber,
            RewardType = (int)rewardType,
            CalculationType = (int)calculationType,
            DiscountValueType = (int)discountValueType,
            DiscountPercent = discountPercent,
            DiscountAmount = discountAmount,
            TierLevel = tierLevel,
            MaxRewardAmount = maxRewardAmount
        };
    }

    /// <summary>
    /// Calculate Back Margin reward amount
    /// </summary>
    public static decimal CalculateBackMarginReward(
        PromotionCondition condition,
        PromotionReward reward,
        decimal actualAmount)
    {
        if (condition.ConditionType == (int)PromotionConditionTypeEnum.BackMarginEarlyPayment)
        {
            // Early payment discount
            return actualAmount * (reward.DiscountPercent ?? 0) / 100;
        }

        return reward.CalculationType switch
        {
            (int)BackMarginCalculationTypeEnum.Progressive => CalculateProgressiveReward(condition, reward, actualAmount),
            (int)BackMarginCalculationTypeEnum.Stepwise => CalculateStepwiseReward(condition, reward, actualAmount),
            _ => 0
        };
    }

    /// <summary>
    /// Calculate progressive reward (lũy tiến)
    /// </summary>
    private static decimal CalculateProgressiveReward(
        PromotionCondition condition,
        PromotionReward reward,
        decimal actualAmount)
    {
        if (!condition.MilestoneAmount.HasValue || condition.MilestoneAmount.Value == 0)
            return 0;

        var multiplier = Math.Floor(actualAmount / condition.MilestoneAmount.Value);
        
        var rewardAmount = reward.DiscountValueType == (int)DiscountValueTypeEnum.Percentage
            ? actualAmount * (reward.DiscountPercent ?? 0) / 100 * multiplier
            : (reward.DiscountAmount ?? 0) * multiplier;

        // Apply max reward limit
        if (reward.MaxRewardAmount.HasValue && rewardAmount > reward.MaxRewardAmount.Value)
            rewardAmount = reward.MaxRewardAmount.Value;

        return rewardAmount;
    }

    /// <summary>
    /// Calculate stepwise reward (bậc thang)
    /// </summary>
    private static decimal CalculateStepwiseReward(
        PromotionCondition condition,
        PromotionReward reward,
        decimal actualAmount)
    {
        // Check if amount falls within the tier range
        if (condition.MinAmount.HasValue && actualAmount < condition.MinAmount.Value)
            return 0;

        if (condition.MaxAmount.HasValue && actualAmount > condition.MaxAmount.Value)
            return 0;

        var rewardAmount = reward.DiscountValueType == (int)DiscountValueTypeEnum.Percentage
            ? actualAmount * (reward.DiscountPercent ?? 0) / 100
            : reward.DiscountAmount ?? 0;

        // Apply max reward limit
        if (reward.MaxRewardAmount.HasValue && rewardAmount > reward.MaxRewardAmount.Value)
            rewardAmount = reward.MaxRewardAmount.Value;

        return rewardAmount;
    }

    /// <summary>
    /// Get display name for Back Margin discount type
    /// </summary>
    public static string GetBackMarginDiscountTypeName(BackMarginDiscountTypeEnum discountType)
    {
        return discountType switch
        {
            BackMarginDiscountTypeEnum.RevenueProgressive => "Chiết khấu theo doanh số lũy tiến",
            BackMarginDiscountTypeEnum.RevenueStepwise => "Chiết khấu theo doanh số bậc thang",
            BackMarginDiscountTypeEnum.QuantityProgressive => "Chiết khấu theo số lượng lũy tiến",
            BackMarginDiscountTypeEnum.QuantityStepwise => "Chiết khấu theo số lượng bậc thang",
            BackMarginDiscountTypeEnum.EarlyPayment => "Chiết khấu thanh toán sớm",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get display name for reward type
    /// </summary>
    public static string GetRewardTypeName(PromotionRewardTypeEnum rewardType)
    {
        return rewardType switch
        {
            PromotionRewardTypeEnum.Cash => "Tiền mặt",
            PromotionRewardTypeEnum.Transfer => "Chuyển khoản",
            PromotionRewardTypeEnum.DebtOffset => "Trừ công nợ",
            PromotionRewardTypeEnum.Goods => "Hàng",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get display name for calculation type
    /// </summary>
    public static string GetCalculationTypeName(BackMarginCalculationTypeEnum calculationType)
    {
        return calculationType switch
        {
            BackMarginCalculationTypeEnum.Progressive => "Lũy tiến",
            BackMarginCalculationTypeEnum.Stepwise => "Bậc thang",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Validate Back Margin condition and reward
    /// </summary>
    public static List<string> ValidateBackMarginSetup(PromotionCondition condition, PromotionReward reward)
    {
        var errors = new List<string>();

        // Validate condition
        if (string.IsNullOrWhiteSpace(condition.ItemNumber))
            errors.Add("Mã sản phẩm không được để trống");

        switch (condition.ConditionType)
        {
            case (int)PromotionConditionTypeEnum.BackMarginRevenue:
            case (int)PromotionConditionTypeEnum.BackMarginQuantity:
                if (reward.CalculationType == (int)BackMarginCalculationTypeEnum.Progressive)
                {
                    if (!condition.MilestoneAmount.HasValue || condition.MilestoneAmount.Value <= 0)
                        errors.Add("Doanh số/số lượng mốc phải lớn hơn 0");
                }
                else if (reward.CalculationType == (int)BackMarginCalculationTypeEnum.Stepwise)
                {
                    if (!condition.MinAmount.HasValue || !condition.MaxAmount.HasValue)
                        errors.Add("Phải có giá trị tối thiểu và tối đa cho bậc thang");
                    
                    if (condition.MinAmount.HasValue && condition.MaxAmount.HasValue && 
                        condition.MinAmount.Value >= condition.MaxAmount.Value)
                        errors.Add("Giá trị tối thiểu phải nhỏ hơn giá trị tối đa");
                }
                break;

            case (int)PromotionConditionTypeEnum.BackMarginEarlyPayment:
                if (!condition.EarlyPaymentDays.HasValue || condition.EarlyPaymentDays.Value <= 0)
                    errors.Add("Số ngày thanh toán sớm phải lớn hơn 0");
                break;
        }

        // Validate reward
        if (reward.DiscountValueType == (int)DiscountValueTypeEnum.Percentage)
        {
            if (!reward.DiscountPercent.HasValue || reward.DiscountPercent.Value <= 0)
                errors.Add("Tỷ lệ chiết khấu phải lớn hơn 0");
        }
        else if (reward.DiscountValueType == (int)DiscountValueTypeEnum.FixedAmount)
        {
            if (!reward.DiscountAmount.HasValue || reward.DiscountAmount.Value <= 0)
                errors.Add("Số tiền chiết khấu phải lớn hơn 0");
        }

        return errors;
    }

    /// <summary>
    /// Get all Back Margin discount types
    /// </summary>
    public static Dictionary<int, string> GetBackMarginDiscountTypes()
    {
        return new Dictionary<int, string>
        {
            { (int)BackMarginDiscountTypeEnum.RevenueProgressive, "Chiết khấu theo doanh số lũy tiến" },
            { (int)BackMarginDiscountTypeEnum.RevenueStepwise, "Chiết khấu theo doanh số bậc thang" },
            { (int)BackMarginDiscountTypeEnum.QuantityProgressive, "Chiết khấu theo số lượng lũy tiến" },
            { (int)BackMarginDiscountTypeEnum.QuantityStepwise, "Chiết khấu theo số lượng bậc thang" },
            { (int)BackMarginDiscountTypeEnum.EarlyPayment, "Chiết khấu thanh toán sớm" }
        };
    }

    /// <summary>
    /// Get Back Margin reward types
    /// </summary>
    public static Dictionary<int, string> GetBackMarginRewardTypes()
    {
        return new Dictionary<int, string>
        {
            { (int)PromotionRewardTypeEnum.Cash, "Tiền mặt" },
            { (int)PromotionRewardTypeEnum.Transfer, "Chuyển khoản" },
            { (int)PromotionRewardTypeEnum.DebtOffset, "Trừ công nợ" },
            { (int)PromotionRewardTypeEnum.Goods, "Hàng" }
        };
    }
}
