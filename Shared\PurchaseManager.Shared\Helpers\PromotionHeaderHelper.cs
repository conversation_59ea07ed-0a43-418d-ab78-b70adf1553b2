using PurchaseManager.Constants.Enum;
using PurchaseManager.Infrastructure.Storage.DataModels;

namespace PurchaseManager.Shared.Helpers;

public static class PromotionHeaderHelper
{
    /// <summary>
    /// Validate promotion header based on program type
    /// </summary>
    public static List<string> ValidatePromotionHeader(PromotionHeader header)
    {
        var errors = new List<string>();

        // Common validations
        if (string.IsNullOrWhiteSpace(header.ProgramName))
            errors.Add("Tên chương trình không được để trống");

        if (string.IsNullOrWhiteSpace(header.Description))
            errors.Add("Mô tả không được để trống");

        if (string.IsNullOrWhiteSpace(header.VendorCode))
            errors.Add("Mã nhà cung cấp không được để trống");

        if (header.StartDate >= header.EndDate)
            errors.Add("<PERSON>ày bắt đầu phải nhỏ hơn ngày kết thúc");

        if (header.StartDate < DateTime.Now.Date)
            errors.Add("<PERSON><PERSON><PERSON> bắt đầu không được nhỏ hơn ngày hiện tại");

        // Program type specific validations
        switch (header.ProgramType)
        {
            case (int)PromotionProgramTypeEnum.FrontMargin:
                ValidateFrontMarginHeader(header, errors);
                break;
            case (int)PromotionProgramTypeEnum.BackMargin:
                ValidateBackMarginHeader(header, errors);
                break;
        }

        return errors;
    }

    /// <summary>
    /// Validate Front Margin specific fields
    /// </summary>
    private static void ValidateFrontMarginHeader(PromotionHeader header, List<string> errors)
    {
        // Front Margin không sử dụng các field BackMargin-specific
        if (header.DefaultPaymentMethod.HasValue)
            errors.Add("Front Margin không sử dụng DefaultPaymentMethod (logic trong PromotionFrontMargin)");

        if (header.DefaultPaymentTiming.HasValue)
            errors.Add("Front Margin không sử dụng DefaultPaymentTiming (luôn áp dụng ngay)");

        if (header.EvaluationPeriod.HasValue)
            errors.Add("Front Margin không sử dụng EvaluationPeriod");

        if (header.AccumulateRevenue)
            errors.Add("Front Margin không tích lũy doanh số");

        // Support type không bắt buộc cho Front Margin
    }

    /// <summary>
    /// Validate Back Margin specific fields
    /// </summary>
    private static void ValidateBackMarginHeader(PromotionHeader header, List<string> errors)
    {
        // Support type is required for Back Margin
        if (string.IsNullOrWhiteSpace(header.SupportTypeNumber))
            errors.Add("Back Margin phải chọn loại hỗ trợ");

        // EvaluationPeriod is recommended for Back Margin
        if (!header.EvaluationPeriod.HasValue)
            errors.Add("Back Margin nên chọn chu kỳ đánh giá (Monthly/Quarterly/Yearly)");

        // DefaultPaymentMethod và DefaultPaymentTiming là optional (có thể set trong PromotionReward)
        if (header.DefaultPaymentMethod.HasValue && header.DefaultPaymentMethod == (int)PromotionPaymentMethodEnum.Discount)
            errors.Add("Back Margin không hỗ trợ hình thức giảm giá trực tiếp");

        if (header.DefaultPaymentTiming.HasValue && header.DefaultPaymentTiming == (int)PromotionPaymentTimingEnum.Immediate)
            errors.Add("Back Margin không hỗ trợ chi trả ngay lập tức");

        // MinOrderValue, MaxOrderValue không sử dụng cho BackMargin
        if (header.MinOrderValue.HasValue || header.MaxOrderValue.HasValue)
            errors.Add("Back Margin không sử dụng MinOrderValue/MaxOrderValue (logic trong PromotionCondition)");
    }

    /// <summary>
    /// Set default values for Front Margin
    /// </summary>
    public static void SetFrontMarginDefaults(PromotionHeader header)
    {
        header.ProgramType = (int)PromotionProgramTypeEnum.FrontMargin;
        header.RequireApprovalBeforeReward = false; // Front Margin usually auto-apply
        header.AutoApply = true;
        header.AccumulateRevenue = false; // Front Margin không tích lũy

        // Không set DefaultPaymentMethod, DefaultPaymentTiming, EvaluationPeriod
        // Vì Front Margin không sử dụng
    }

    /// <summary>
    /// Set default values for Back Margin
    /// </summary>
    public static void SetBackMarginDefaults(PromotionHeader header)
    {
        header.ProgramType = (int)PromotionProgramTypeEnum.BackMargin;
        header.DefaultPaymentMethod = (int)PromotionPaymentMethodEnum.Transfer; // Default to bank transfer
        header.DefaultPaymentTiming = (int)PromotionPaymentTimingEnum.Plus30Days; // Default to 30 days
        header.EvaluationPeriod = (int)PromotionCalculationPeriodEnum.Quarterly; // Default quarterly
        header.RequireApprovalBeforeReward = true; // Back Margin usually requires approval
        header.AutoApply = false; // Back Margin usually manual
        header.AccumulateRevenue = true; // Back Margin thường tích lũy doanh số
    }

    /// <summary>
    /// Get display name for default payment method
    /// </summary>
    public static string GetDefaultPaymentMethodName(int? paymentMethod)
    {
        if (!paymentMethod.HasValue) return "Chưa chọn";

        return paymentMethod.Value switch
        {
            (int)PromotionPaymentMethodEnum.Cash => "Tiền mặt",
            (int)PromotionPaymentMethodEnum.Transfer => "Chuyển khoản",
            (int)PromotionPaymentMethodEnum.DebtOffset => "Trừ công nợ",
            (int)PromotionPaymentMethodEnum.Goods => "Hàng",
            (int)PromotionPaymentMethodEnum.Discount => "Giảm giá trực tiếp",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get display name for default payment timing
    /// </summary>
    public static string GetDefaultPaymentTimingName(int? paymentTiming)
    {
        if (!paymentTiming.HasValue) return "Chưa chọn";

        return paymentTiming.Value switch
        {
            (int)PromotionPaymentTimingEnum.Immediate => "Ngay lập tức",
            (int)PromotionPaymentTimingEnum.NextOrder => "Đơn hàng tiếp theo",
            (int)PromotionPaymentTimingEnum.AfterPayment => "Sau khi thanh toán đơn hàng",
            (int)PromotionPaymentTimingEnum.Plus30Days => "n + 30 ngày",
            (int)PromotionPaymentTimingEnum.Plus60Days => "n + 60 ngày",
            (int)PromotionPaymentTimingEnum.Plus90Days => "n + 90 ngày",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get display name for evaluation period
    /// </summary>
    public static string GetEvaluationPeriodName(int? evaluationPeriod)
    {
        if (!evaluationPeriod.HasValue) return "Chưa chọn";

        return evaluationPeriod.Value switch
        {
            (int)PromotionCalculationPeriodEnum.Monthly => "Hàng tháng",
            (int)PromotionCalculationPeriodEnum.Quarterly => "Hàng quý",
            (int)PromotionCalculationPeriodEnum.Yearly => "Hàng năm",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get display name for status
    /// </summary>
    public static string GetStatusName(int status)
    {
        return status switch
        {
            (int)PromotionStatusEnum.Draft => "Nháp",
            (int)PromotionStatusEnum.Active => "Đang áp dụng",
            (int)PromotionStatusEnum.Inactive => "Hết hiệu lực",
            (int)PromotionStatusEnum.Expired => "Hết hạn",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Check if promotion is currently active
    /// </summary>
    public static bool IsActive(PromotionHeader header)
    {
        var now = DateTime.Now;
        return header.Status == (int)PromotionStatusEnum.Active &&
               header.StartDate <= now &&
               header.EndDate >= now;
    }

    /// <summary>
    /// Check if promotion has expired
    /// </summary>
    public static bool IsExpired(PromotionHeader header)
    {
        return DateTime.Now > header.EndDate;
    }

    /// <summary>
    /// Get available default payment methods for Back Margin
    /// </summary>
    public static Dictionary<int, string> GetAvailableDefaultPaymentMethods()
    {
        return new Dictionary<int, string>
        {
            { (int)PromotionPaymentMethodEnum.Cash, "Tiền mặt" },
            { (int)PromotionPaymentMethodEnum.Transfer, "Chuyển khoản" },
            { (int)PromotionPaymentMethodEnum.DebtOffset, "Trừ công nợ" },
            { (int)PromotionPaymentMethodEnum.Goods, "Hàng" }
        };
    }

    /// <summary>
    /// Get available default payment timings for Back Margin
    /// </summary>
    public static Dictionary<int, string> GetAvailableDefaultPaymentTimings()
    {
        return new Dictionary<int, string>
        {
            { (int)PromotionPaymentTimingEnum.NextOrder, "Đơn hàng tiếp theo" },
            { (int)PromotionPaymentTimingEnum.AfterPayment, "Sau khi thanh toán đơn hàng" },
            { (int)PromotionPaymentTimingEnum.Plus30Days, "n + 30 ngày" },
            { (int)PromotionPaymentTimingEnum.Plus60Days, "n + 60 ngày" },
            { (int)PromotionPaymentTimingEnum.Plus90Days, "n + 90 ngày" }
        };
    }

    /// <summary>
    /// Get available evaluation periods for Back Margin
    /// </summary>
    public static Dictionary<int, string> GetAvailableEvaluationPeriods()
    {
        return new Dictionary<int, string>
        {
            { (int)PromotionCalculationPeriodEnum.Monthly, "Hàng tháng" },
            { (int)PromotionCalculationPeriodEnum.Quarterly, "Hàng quý" },
            { (int)PromotionCalculationPeriodEnum.Yearly, "Hàng năm" }
        };
    }

    /// <summary>
    /// Get program type display name
    /// </summary>
    public static string GetProgramTypeName(int programType)
    {
        return programType switch
        {
            (int)PromotionProgramTypeEnum.FrontMargin => "Front Margin",
            (int)PromotionProgramTypeEnum.BackMargin => "Back Margin",
            _ => "Không xác định"
        };
    }
}
